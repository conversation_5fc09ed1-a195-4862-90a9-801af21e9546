<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周边产品展示 - 普洱蘑菇庄园民宿</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .category-nav {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-tab:hover {
            border-color: #007bff;
            color: #007bff;
            transform: translateY(-2px);
        }

        .nav-tab.active {
            background: #007bff;
            border-color: #007bff;
            color: white;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .product-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }

        .image-gallery {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .image-gallery img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0;
            transition: opacity 0.3s ease, transform 0.3s ease;
            cursor: pointer;
        }

        .image-gallery img.active {
            opacity: 1;
        }

        .image-gallery img:hover {
            transform: scale(1.05);
        }

        .single-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .single-image {
            transform: scale(1.05);
        }

        .product-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #ff6b6b;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }

        .gallery-indicator {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
        }

        .gallery-indicator .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transition: background-color 0.3s ease;
            cursor: pointer;
        }

        .gallery-indicator .dot.active {
            background: white;
        }

        .product-info {
            padding: 20px;
        }

        .product-info h3 {
            margin: 0 0 10px 0;
            font-size: 1.3rem;
            color: #333;
        }

        .product-desc {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .product-price {
            margin-bottom: 20px;
        }

        .current-price {
            font-size: 1.5rem;
            font-weight: 600;
            color: #e74c3c;
        }

        .original-price {
            margin-left: 10px;
            text-decoration: line-through;
            color: #999;
        }

        .product-actions {
            display: flex;
            gap: 10px;
        }

        .btn-primary, .btn-secondary {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .quick-links {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .link-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .link-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            color: white;
        }

        .link-btn.primary {
            background: #007bff;
        }

        .link-btn.success {
            background: #28a745;
        }

        .link-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats-section {
            background: #fff3cd;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .category-nav {
                flex-direction: column;
                align-items: center;
            }

            .product-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛍️ 周边产品展示</h1>
            <p>精选普洱蘑菇庄园特色产品，带走美好回忆</p>
        </div>

        <div class="quick-links">
            <h3>🚀 快速访问</h3>
            <div class="link-buttons">
                <a href="http://localhost:5173/products" class="link-btn primary" target="_blank">
                    🛍️ 完整产品页面
                </a>
                <a href="http://localhost:5173/" class="link-btn success" target="_blank">
                    🏠 返回主页
                </a>
            </div>
        </div>

        <!-- 产品分类导航 -->
        <div class="category-nav">
            <button class="nav-tab active" onclick="showCategory('tea')">
                <i class="fas fa-leaf"></i>
                茶叶产品
            </button>
            <button class="nav-tab" onclick="showCategory('ip')">
                <i class="fas fa-star"></i>
                IP衍生品
            </button>
            <button class="nav-tab" onclick="showCategory('souvenir')">
                <i class="fas fa-gift"></i>
                纪念品
            </button>
        </div>

        <!-- 茶叶产品 -->
        <div id="tea-products" class="product-grid">
            <div class="product-card">
                <div class="product-image">
                    <div class="image-gallery">
                        <img src="/src/assets/images/产品/茶饼/茶饼包装.jpg" alt="精品普洱茶饼" class="active">
                        <img src="/src/assets/images/产品/茶饼/茶饼原料1.jpg" alt="茶饼原料">
                        <img src="/src/assets/images/产品/茶饼/茶饼原料2.jpg" alt="茶饼原料">
                        <img src="/src/assets/images/产品/茶饼/茶饼原料3.jpg" alt="茶饼原料">
                    </div>
                    <div class="product-badge">热销</div>
                    <div class="gallery-indicator">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
                <div class="product-info">
                    <h3>精品普洱茶饼</h3>
                    <p class="product-desc">选用优质大叶种茶叶，传统工艺制作</p>
                    <div class="product-price">
                        <span class="current-price">¥168</span>
                        <span class="original-price">¥198</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="image-gallery">
                        <img src="/src/assets/images/产品/普洱茶/普洱茶1.jpg" alt="散装普洱茶" class="active">
                        <img src="/src/assets/images/产品/普洱茶/普洱茶2.jpg" alt="普洱茶">
                        <img src="/src/assets/images/产品/普洱茶/普洱茶3.jpg" alt="普洱茶">
                    </div>
                    <div class="product-badge">新品</div>
                    <div class="gallery-indicator">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
                <div class="product-info">
                    <h3>散装普洱茶（一级）</h3>
                    <p class="product-desc">新鲜采摘，自然晒干，原汁原味</p>
                    <div class="product-price">
                        <span class="current-price">¥88</span>
                        <span class="original-price">¥108</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- IP衍生品 -->
        <div id="ip-products" class="product-grid" style="display: none;">
            <div class="product-card">
                <div class="product-image">
                    <div class="image-gallery">
                        <img src="/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg" alt="茶茶主题帆布包" class="active">
                        <img src="/src/assets/images/IP形象衍生产品/帆布包/帆布包2.png" alt="帆布包">
                        <img src="/src/assets/images/IP形象衍生产品/帆布包/帆布包3.jpg" alt="帆布包">
                    </div>
                    <div class="product-badge">热销</div>
                    <div class="gallery-indicator">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
                <div class="product-info">
                    <h3>茶茶主题帆布包</h3>
                    <p class="product-desc">可爱IP形象，实用环保</p>
                    <div class="product-price">
                        <span class="current-price">¥58</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <div class="image-gallery">
                        <img src="/src/assets/images/IP形象衍生产品/挂坠/挂坠1.jpg" alt="茶茶纪念挂坠" class="active">
                        <img src="/src/assets/images/IP形象衍生产品/挂坠/挂坠2.jpg" alt="挂坠">
                        <img src="/src/assets/images/IP形象衍生产品/挂坠/挂坠展示.jpg" alt="挂坠展示">
                    </div>
                    <div class="product-badge">新品</div>
                    <div class="gallery-indicator">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                        <span class="dot"></span>
                    </div>
                </div>
                <div class="product-info">
                    <h3>茶茶纪念挂坠</h3>
                    <p class="product-desc">精美挂坠，随身携带</p>
                    <div class="product-price">
                        <span class="current-price">¥28</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 纪念品 -->
        <div id="souvenir-products" class="product-grid" style="display: none;">
            <div class="product-card">
                <div class="product-image">
                    <div class="image-gallery">
                        <img src="/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg" alt="庄园纪念勋章" class="active">
                        <img src="/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg" alt="勋章">
                    </div>
                    <div class="product-badge">限量</div>
                    <div class="gallery-indicator">
                        <span class="dot active"></span>
                        <span class="dot"></span>
                    </div>
                </div>
                <div class="product-info">
                    <h3>庄园纪念勋章（金色版）</h3>
                    <p class="product-desc">独特设计，收藏价值</p>
                    <div class="product-price">
                        <span class="current-price">¥38</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>

            <div class="product-card">
                <div class="product-image">
                    <img src="/src/assets/images/IP形象衍生产品/水杯/水杯1.jpg" alt="茶茶定制水杯" class="single-image">
                    <div class="product-badge">实用</div>
                </div>
                <div class="product-info">
                    <h3>茶茶定制水杯</h3>
                    <p class="product-desc">保温效果好，设计精美</p>
                    <div class="product-price">
                        <span class="current-price">¥88</span>
                        <span class="original-price">¥108</span>
                    </div>
                    <div class="product-actions">
                        <button class="btn-primary">立即购买</button>
                        <button class="btn-secondary">加入购物车</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h3>📊 产品统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value">12</div>
                    <div class="stat-label">茶叶产品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">18</div>
                    <div class="stat-label">IP衍生品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">8</div>
                    <div class="stat-label">纪念品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">38</div>
                    <div class="stat-label">总产品数</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分类切换功能
        function showCategory(category) {
            // 隐藏所有产品区域
            document.getElementById('tea-products').style.display = 'none';
            document.getElementById('ip-products').style.display = 'none';
            document.getElementById('souvenir-products').style.display = 'none';

            // 移除所有导航按钮的active类
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的产品区域
            document.getElementById(category + '-products').style.display = 'grid';

            // 添加active类到选中的导航按钮
            event.target.classList.add('active');
        }

        // 图片切换功能
        function switchImage(event, container) {
            const target = event.target;
            const images = container.querySelectorAll('.image-gallery img');
            const dots = container.querySelectorAll('.gallery-indicator .dot');
            
            // 移除所有active类
            images.forEach((img, index) => {
                img.classList.remove('active');
                if (dots[index]) {
                    dots[index].classList.remove('active');
                }
            });
            
            // 添加active类到当前图片
            target.classList.add('active');
            const imageIndex = Array.from(images).indexOf(target);
            if (dots[imageIndex]) {
                dots[imageIndex].classList.add('active');
            }
        }

        // 为所有图片添加鼠标悬停事件
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.image-gallery img').forEach(img => {
                img.addEventListener('mouseenter', function(event) {
                    const container = event.target.closest('.product-image');
                    switchImage(event, container);
                });
            });
        });
    </script>
</body>
</html>
