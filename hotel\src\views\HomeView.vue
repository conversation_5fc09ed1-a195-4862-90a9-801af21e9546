<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 轮播图区域 -->
      <section class="hero-section">
        <div class="carousel-container">
          <div class="carousel-slide active">
            <img src="@/assets/images/环境展示/蘑菇庄园门口展示1.jpg" alt="蘑菇庄园门口">
          </div>
          <div class="carousel-slide">
            <img src="@/assets/images/环境展示/庄园内部1.jpg" alt="庄园内部环境">
          </div>
          <div class="carousel-slide">
            <img src="@/assets/images/实地调研/室外调研/室外1.jpg" alt="庄园外景">
          </div>
          <div class="carousel-slide">
            <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研1.jpg" alt="茶叶产品展示">
          </div>
          <div class="carousel-controls">
            <button class="prev-btn carousel-btn">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button class="next-btn carousel-btn">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          <div class="carousel-dots"></div>
          <div class="hero-content">
            <h1 class="hero-title">普洱蘑菇庄园民宿</h1>
            <p class="hero-subtitle">隐于森林深处，与自然共生的生态民宿体验</p>
            <div class="hero-buttons">
              <button @click="handleBookingClick" class="btn btn-primary btn-large">立即预订</button>
              <button class="btn btn-outline-white btn-large">探索庄园</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 庄园介绍区域 -->
      <section class="intro-section">
        <div class="container">
          <h2 class="section-title">普洱蘑菇庄园民宿生态体验</h2>
          <p class="intro-text">
            坐落在普洱茶山深处的生态庄园，这里有原始森林中的蘑菇小屋、茶香四溢的品茶空间，
            以及丰富的蘑菇采摘体验。每一间小屋都融入自然环境，让您在享受现代舒适的同时，
            深度体验云南的生态文化与茶乡风情。
          </p>
          <div class="intro-buttons">
            <button @click="handleBookingClick" class="btn btn-primary btn-large">立即预订</button>
            <button @click="handleLearnMoreClick" class="btn btn-outline btn-large">了解更多</button>
          </div>
        </div>
      </section>

      <!-- 庄园设施展示区域 -->
      <section class="facilities-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">生态设施</h2>
            <p class="section-subtitle">享受原生态的自然环境与贴心的生态服务</p>
          </div>

          <!-- 主要设施图片 -->
          <div class="facilities-main">
            <div class="facility-card facility-large" @click="handleBookingClick">
              <img src="@/assets/images/实地调研/房间参观/房间参观1.jpg" alt="蘑菇森林小屋">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h3 class="facility-title">蘑菇森林小屋</h3>
                <p class="facility-desc">隐于原始森林中的生态小屋，与自然零距离接触</p>
              </div>
            </div>
            <div class="facility-card facility-large" @click="handleTeaCultureClick">
              <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研5.jpg" alt="普洱茶园">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h3 class="facility-title">普洱茶园</h3>
                <p class="facility-desc">千年茶树环绕，品味正宗普洱茶文化</p>
              </div>
            </div>
          </div>

          <!-- 次要设施图片 -->
          <div class="facilities-grid">
            <div class="facility-card facility-small">
              <img src="@/assets/images/环境展示/庄园内部1.jpg" alt="蘑菇生态园">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">蘑菇生态园</h4>
              </div>
            </div>
            <div class="facility-card facility-small">
              <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研12.jpg" alt="茶文化体验馆">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">茶文化体验馆</h4>
              </div>
            </div>
            <div class="facility-card facility-small">
              <img src="@/assets/images/环境展示/酒店门口2.jpg" alt="生态餐厅">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">生态餐厅</h4>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 客户评价区域 -->
      <section class="reviews-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">客户评价</h2>
            <p class="section-subtitle">听听客人们的真实体验</p>
          </div>

          <div class="reviews-grid">
            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">5.0</span>
              </div>
              <p class="review-text">"环境优美，服务贴心，茶道体验让人印象深刻。这里真的是放松身心的绝佳去处。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">张女士</p>
                  <p class="author-location">上海</p>
                </div>
              </div>
            </div>

            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">5.0</span>
              </div>
              <p class="review-text">"山景房的视野太棒了！早上的瑜伽课程让我感受到了前所未有的宁静。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">李先生</p>
                  <p class="author-location">北京</p>
                </div>
              </div>
            </div>

            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">4.9</span>
              </div>
              <p class="review-text">"烹饪课程太有趣了！学会了正宗的云南菜做法，回家也能做给家人吃。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">王女士</p>
                  <p class="author-location">广州</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-logo">普洱蘑菇庄园</h3>
            <p class="footer-desc">云南山居度假体验的领导者，为您提供难忘的奢华住宿体验。</p>
            <div class="social-links">
              <a href="#" class="social-link">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-weibo"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-wechat"></i>
              </a>
            </div>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">快速链接</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">精品客房</a></li>
              <li><a href="#" class="footer-link">餐饮美食</a></li>
              <li><a href="#" class="footer-link">休闲娱乐</a></li>
              <li><a href="#" class="footer-link">会议宴会</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">服务支持</h4>
            <ul class="footer-links">
              <li><a @click.prevent="handleBookingClick" href="#" class="footer-link">在线预订</a></li>
              <li><a href="#" class="footer-link">客户服务</a></li>
              <li><a href="#" class="footer-link">常见问题</a></li>
              <li><a href="#" class="footer-link">联系我们</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">联系信息</h4>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>云南省大理市古城区</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>************</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="footer-bottom-content">
            <p>&copy; 2025 普洱蘑菇庄园民宿. 保留所有权利.</p>
            <router-link to="/admin/login" class="admin-entrance-link">
              管理入口
            </router-link>
          </div>
        </div>
      </div>
    </footer>

    <!-- 全局IP助手 -->
    <!-- <GlobalIPAssistant /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import AppNavbar from '../components/AppNavbar.vue'
// import GlobalIPAssistant from '../components/GlobalIPAssistant.vue'

const router = useRouter()
const auth = useAuthStore()

// 轮播图状态
const currentSlide = ref(0)
const totalSlides = 4
let slideInterval: number | null = null

// 处理预订按钮点击
const handleBookingClick = () => {
  if (auth.isAuthenticated) {
    router.push('/ai-rooms')
  } else {
    router.push('/login')
  }
}

// 处理了解更多按钮点击
const handleLearnMoreClick = () => {
  router.push('/ai-rooms')
}

// 处理茶文化点击
const handleTeaCultureClick = () => {
  if (auth.isAuthenticated) {
    router.push('/tea-culture')
  } else {
    router.push('/login')
  }
}

// 轮播图功能
const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % totalSlides
  updateSlides()
}

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + totalSlides) % totalSlides
  updateSlides()
}

const goToSlide = (index: number) => {
  currentSlide.value = index
  updateSlides()
}

const updateSlides = () => {
  const slides = document.querySelectorAll('.carousel-slide')
  const dots = document.querySelectorAll('.carousel-dot')

  slides.forEach((slide, index) => {
    slide.classList.toggle('active', index === currentSlide.value)
  })

  dots.forEach((dot, index) => {
    dot.classList.toggle('active', index === currentSlide.value)
  })
}

const startAutoSlide = () => {
  slideInterval = setInterval(nextSlide, 5000) // 每5秒切换
}

const stopAutoSlide = () => {
  if (slideInterval) {
    clearInterval(slideInterval)
    slideInterval = null
  }
}

// 初始化轮播图
const initCarousel = () => {
  // 创建点指示器
  const dotsContainer = document.querySelector('.carousel-dots')
  if (dotsContainer) {
    dotsContainer.innerHTML = ''
    for (let i = 0; i < totalSlides; i++) {
      const dot = document.createElement('div')
      dot.className = `carousel-dot ${i === 0 ? 'active' : ''}`
      dot.addEventListener('click', () => goToSlide(i))
      dotsContainer.appendChild(dot)
    }
  }

  // 添加按钮事件
  const prevBtn = document.querySelector('.prev-btn')
  const nextBtn = document.querySelector('.next-btn')

  if (prevBtn) prevBtn.addEventListener('click', prevSlide)
  if (nextBtn) nextBtn.addEventListener('click', nextSlide)

  // 开始自动播放
  startAutoSlide()

  // 鼠标悬停时停止自动播放
  const carouselContainer = document.querySelector('.carousel-container')
  if (carouselContainer) {
    carouselContainer.addEventListener('mouseenter', stopAutoSlide)
    carouselContainer.addEventListener('mouseleave', startAutoSlide)
  }
}

onMounted(() => {
  initCarousel()
})

onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
/* 基础样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.main-content {
  flex: 1; /* 确保主内容区域占据剩余空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 600px;
  overflow: hidden;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 轮播图控制 */
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 5;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  color: #333;
}

.carousel-btn:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.1);
}

/* 轮播图点指示器 */
.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 5;
}

.carousel-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background: white;
  transform: scale(1.2);
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 10;
  width: 90%;
  max-width: 800px;
}

.hero-title {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 24px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 32px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: var(--color-primary, #d4af37);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark, #b8941f);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary, #d4af37);
  border: 2px solid var(--color-primary, #d4af37);
}

.btn-outline:hover {
  background-color: var(--color-primary, #d4af37);
  color: white;
}

.btn-outline-white {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-outline-white:hover {
  background-color: white;
  color: var(--color-primary, #d4af37);
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

/* 通用区域样式 */
.intro-section,
.facilities-section,
.reviews-section {
  padding: 80px 0;
}

.intro-section {
  background-color: #f9fafb;
  text-align: center;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
}

.intro-text {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto 40px;
}

.intro-buttons {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 40px;
}

/* 页脚样式 */
.footer {
  background-color: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--color-primary, #d4af37);
  margin-bottom: 16px;
}

.footer-desc {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 24px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  text-align: center;
  color: #9ca3af;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.admin-entrance-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.admin-entrance-link:hover {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  opacity: 1;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
  }
  
  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}
</style>
