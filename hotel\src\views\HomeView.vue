<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 现代化英雄区域 -->
      <section class="hero-section">
        <div class="hero-background">
          <div class="carousel-container">
            <div class="carousel-slide active" data-bg="@/assets/images/环境展示/蘑菇庄园门口展示1.jpg">
              <div class="slide-overlay"></div>
            </div>
            <div class="carousel-slide" data-bg="@/assets/images/环境展示/庄园内部1.jpg">
              <div class="slide-overlay"></div>
            </div>
            <div class="carousel-slide" data-bg="@/assets/images/实地调研/室外调研/室外1.jpg">
              <div class="slide-overlay"></div>
            </div>
            <div class="carousel-slide" data-bg="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研1.jpg">
              <div class="slide-overlay"></div>
            </div>
          </div>

          <!-- 装饰性粒子效果 -->
          <div class="hero-particles">
            <div class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></div>
          </div>

          <!-- 浮动装饰元素 -->
          <div class="floating-elements">
            <div class="floating-leaf floating-leaf-1"></div>
            <div class="floating-leaf floating-leaf-2"></div>
            <div class="floating-leaf floating-leaf-3"></div>
          </div>
        </div>

        <!-- 英雄内容 -->
        <div class="hero-content-wrapper">
          <div class="hero-content">
            <div class="hero-badge">
              <span class="badge-icon">🍄</span>
              <span class="badge-text">生态民宿体验</span>
            </div>
            <h1 class="hero-title">
              <span class="title-main">普洱蘑菇庄园</span>
              <span class="title-sub">民宿</span>
            </h1>
            <p class="hero-subtitle">
              隐于森林深处，与自然共生<br>
              <span class="subtitle-highlight">体验云南原生态茶乡风情</span>
            </p>
            <div class="hero-features">
              <div class="feature-item">
                <i class="fas fa-leaf"></i>
                <span>原生态环境</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-spa"></i>
                <span>蘑菇采摘</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-coffee"></i>
                <span>普洱茶文化</span>
              </div>
            </div>
            <div class="hero-buttons">
              <button @click="handleBookingClick" class="btn btn-primary btn-large btn-glow">
                <span class="btn-text">立即预订</span>
                <span class="btn-icon">→</span>
              </button>
              <button @click="handleExploreClick" class="btn btn-outline-white btn-large btn-glass">
                <span class="btn-text">探索庄园</span>
                <span class="btn-icon">↓</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 现代化轮播控制 -->
        <div class="carousel-controls">
          <button class="carousel-btn prev-btn" @click="prevSlide">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button class="carousel-btn next-btn" @click="nextSlide">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <!-- 现代化指示器 -->
        <div class="carousel-indicators">
          <button
            v-for="index in 4"
            :key="index"
            :class="['indicator', { active: currentSlide === index }]"
            @click="goToSlide(index)"
          >
            <span class="indicator-progress"></span>
          </button>
        </div>

        <!-- 滚动提示 -->
        <div class="scroll-indicator">
          <div class="scroll-text">向下探索</div>
          <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
          </div>
        </div>
      </section>

      <!-- 现代化介绍区域 -->
      <section class="intro-section">
        <div class="intro-background">
          <div class="intro-pattern"></div>
        </div>
        <div class="container">
          <div class="intro-content">
            <div class="intro-badge">
              <span class="badge-dot"></span>
              <span>生态民宿体验</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">普洱蘑菇庄园</span>
              <span class="title-main">民宿生态体验</span>
            </h2>
            <div class="intro-description">
              <p class="intro-text">
                坐落在普洱茶山深处的生态庄园，这里有原始森林中的蘑菇小屋、茶香四溢的品茶空间，
                以及丰富的蘑菇采摘体验。每一间小屋都融入自然环境，让您在享受现代舒适的同时，
                深度体验云南的生态文化与茶乡风情。
              </p>
            </div>

            <!-- 特色亮点 -->
            <div class="intro-highlights">
              <div class="highlight-item">
                <div class="highlight-icon">
                  <i class="fas fa-seedling"></i>
                </div>
                <div class="highlight-content">
                  <h4>原生态环境</h4>
                  <p>森林深处的纯净空气</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <i class="fas fa-spa"></i>
                </div>
                <div class="highlight-content">
                  <h4>蘑菇采摘体验</h4>
                  <p>亲手采摘新鲜蘑菇</p>
                </div>
              </div>
              <div class="highlight-item">
                <div class="highlight-icon">
                  <i class="fas fa-coffee"></i>
                </div>
                <div class="highlight-content">
                  <h4>普洱茶文化</h4>
                  <p>品味千年茶香文化</p>
                </div>
              </div>
            </div>

            <div class="intro-buttons">
              <button @click="handleBookingClick" class="btn btn-primary btn-large btn-modern">
                <span>立即预订</span>
                <i class="fas fa-arrow-right"></i>
              </button>
              <button @click="handleLearnMoreClick" class="btn btn-outline btn-large btn-modern">
                <span>了解更多</span>
                <i class="fas fa-chevron-down"></i>
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- 庄园设施展示区域 -->
      <section class="facilities-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">生态设施</h2>
            <p class="section-subtitle">享受原生态的自然环境与贴心的生态服务</p>
          </div>

          <!-- 主要设施图片 -->
          <div class="facilities-main">
            <div class="facility-card facility-large" @click="handleBookingClick">
              <img src="@/assets/images/实地调研/房间参观/房间参观1.jpg" alt="蘑菇森林小屋">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h3 class="facility-title">蘑菇森林小屋</h3>
                <p class="facility-desc">隐于原始森林中的生态小屋，与自然零距离接触</p>
              </div>
            </div>
            <div class="facility-card facility-large" @click="handleTeaCultureClick">
              <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研5.jpg" alt="普洱茶园">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h3 class="facility-title">普洱茶园</h3>
                <p class="facility-desc">千年茶树环绕，品味正宗普洱茶文化</p>
              </div>
            </div>
          </div>

          <!-- 次要设施图片 -->
          <div class="facilities-grid">
            <div class="facility-card facility-small">
              <img src="@/assets/images/环境展示/庄园内部1.jpg" alt="蘑菇生态园">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">蘑菇生态园</h4>
              </div>
            </div>
            <div class="facility-card facility-small">
              <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研12.jpg" alt="茶文化体验馆">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">茶文化体验馆</h4>
              </div>
            </div>
            <div class="facility-card facility-small">
              <img src="@/assets/images/环境展示/酒店门口2.jpg" alt="生态餐厅">
              <div class="facility-overlay"></div>
              <div class="facility-content">
                <h4 class="facility-name">生态餐厅</h4>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 客户评价区域 -->
      <section class="reviews-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">客户评价</h2>
            <p class="section-subtitle">听听客人们的真实体验</p>
          </div>

          <div class="reviews-grid">
            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">5.0</span>
              </div>
              <p class="review-text">"环境优美，服务贴心，茶道体验让人印象深刻。这里真的是放松身心的绝佳去处。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">张女士</p>
                  <p class="author-location">上海</p>
                </div>
              </div>
            </div>

            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">5.0</span>
              </div>
              <p class="review-text">"山景房的视野太棒了！早上的瑜伽课程让我感受到了前所未有的宁静。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">李先生</p>
                  <p class="author-location">北京</p>
                </div>
              </div>
            </div>

            <div class="review-card">
              <div class="review-rating">
                <div class="stars">
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                  <i class="fas fa-star"></i>
                </div>
                <span class="rating-text">4.9</span>
              </div>
              <p class="review-text">"烹饪课程太有趣了！学会了正宗的云南菜做法，回家也能做给家人吃。"</p>
              <div class="review-author">
                <div class="author-avatar">
                  <i class="fas fa-user"></i>
                </div>
                <div class="author-info">
                  <p class="author-name">王女士</p>
                  <p class="author-location">广州</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-logo">普洱蘑菇庄园</h3>
            <p class="footer-desc">云南山居度假体验的领导者，为您提供难忘的奢华住宿体验。</p>
            <div class="social-links">
              <a href="#" class="social-link">
                <i class="fab fa-facebook-f"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-weibo"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-wechat"></i>
              </a>
            </div>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">快速链接</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">精品客房</a></li>
              <li><a href="#" class="footer-link">餐饮美食</a></li>
              <li><a href="#" class="footer-link">休闲娱乐</a></li>
              <li><a href="#" class="footer-link">会议宴会</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">服务支持</h4>
            <ul class="footer-links">
              <li><a @click.prevent="handleBookingClick" href="#" class="footer-link">在线预订</a></li>
              <li><a href="#" class="footer-link">客户服务</a></li>
              <li><a href="#" class="footer-link">常见问题</a></li>
              <li><a href="#" class="footer-link">联系我们</a></li>
            </ul>
          </div>

          <div class="footer-section">
            <h4 class="footer-title">联系信息</h4>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>云南省大理市古城区</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>************</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="footer-bottom-content">
            <p>&copy; 2025 普洱蘑菇庄园民宿. 保留所有权利.</p>
            <router-link to="/admin/login" class="admin-entrance-link">
              管理入口
            </router-link>
          </div>
        </div>
      </div>
    </footer>

    <!-- 全局IP助手 -->
    <!-- <GlobalIPAssistant /> -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import AppNavbar from '../components/AppNavbar.vue'
// import GlobalIPAssistant from '../components/GlobalIPAssistant.vue'

const router = useRouter()
const auth = useAuthStore()

// 轮播图状态
const currentSlide = ref(0)
const totalSlides = 4
let slideInterval: number | null = null

// 粒子效果样式生成
const getParticleStyle = (index: number) => {
  const size = Math.random() * 4 + 2
  const left = Math.random() * 100
  const animationDelay = Math.random() * 10
  const animationDuration = Math.random() * 20 + 10

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  }
}

// 处理预订按钮点击
const handleBookingClick = () => {
  if (auth.isAuthenticated) {
    router.push('/ai-rooms')
  } else {
    router.push('/login')
  }
}

// 处理了解更多按钮点击
const handleLearnMoreClick = () => {
  router.push('/ai-rooms')
}

// 处理茶文化点击
const handleTeaCultureClick = () => {
  if (auth.isAuthenticated) {
    router.push('/tea-culture')
  } else {
    router.push('/login')
  }
}

// 处理探索庄园点击
const handleExploreClick = () => {
  // 平滑滚动到介绍区域
  const introSection = document.querySelector('.intro-section')
  if (introSection) {
    introSection.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    })
  }
}

// 轮播图功能
const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % totalSlides
  updateSlides()
}

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + totalSlides) % totalSlides
  updateSlides()
}

const goToSlide = (index: number) => {
  currentSlide.value = index
  updateSlides()
}

const updateSlides = () => {
  const slides = document.querySelectorAll('.carousel-slide')
  const dots = document.querySelectorAll('.carousel-dot')

  slides.forEach((slide, index) => {
    slide.classList.toggle('active', index === currentSlide.value)
  })

  dots.forEach((dot, index) => {
    dot.classList.toggle('active', index === currentSlide.value)
  })
}

const startAutoSlide = () => {
  slideInterval = setInterval(nextSlide, 5000) // 每5秒切换
}

const stopAutoSlide = () => {
  if (slideInterval) {
    clearInterval(slideInterval)
    slideInterval = null
  }
}

// 初始化轮播图
const initCarousel = () => {
  // 设置背景图片
  const slides = document.querySelectorAll('.carousel-slide')
  const images = [
    '/src/assets/images/环境展示/蘑菇庄园门口展示1.jpg',
    '/src/assets/images/环境展示/庄园内部1.jpg',
    '/src/assets/images/实地调研/室外调研/室外1.jpg',
    '/src/assets/images/实地调研/茶叶产品调研/茶叶产品调研1.jpg'
  ]

  slides.forEach((slide, index) => {
    const element = slide as HTMLElement
    element.style.backgroundImage = `url('${images[index]}')`
    element.style.backgroundSize = 'cover'
    element.style.backgroundPosition = 'center'
    element.style.backgroundRepeat = 'no-repeat'
  })

  // 开始自动播放
  startAutoSlide()

  // 鼠标悬停时停止自动播放
  const carouselContainer = document.querySelector('.carousel-container')
  if (carouselContainer) {
    carouselContainer.addEventListener('mouseenter', stopAutoSlide)
    carouselContainer.addEventListener('mouseleave', startAutoSlide)
  }
}

onMounted(() => {
  initCarousel()
})

onUnmounted(() => {
  stopAutoSlide()
})
</script>

<style scoped>
/* 基础样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.main-content {
  padding-top: 70px; /* 为固定导航栏留出空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 现代化英雄区域样式 */
.hero-section {
  position: relative;
  height: 100vh;
  min-height: 700px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.carousel-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: all 1.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform: scale(1.1);
}

.carousel-slide.active {
  opacity: 1;
  transform: scale(1);
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.2) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  z-index: 2;
}

/* 粒子效果 */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 15s infinite linear;
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* 浮动装饰元素 */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}

.floating-leaf {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(212, 175, 55, 0.3);
  border-radius: 50% 0;
  animation: leafFloat 20s infinite ease-in-out;
}

.floating-leaf-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-leaf-2 {
  top: 60%;
  right: 15%;
  animation-delay: 7s;
}

.floating-leaf-3 {
  top: 40%;
  left: 80%;
  animation-delay: 14s;
}

@keyframes leafFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
  75% {
    transform: translateY(-30px) rotate(270deg);
  }
}

/* 英雄内容区域 */
.hero-content-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  text-align: center;
}

.hero-content {
  animation: heroFadeIn 1.5s ease-out;
}

@keyframes heroFadeIn {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.5rem 1.5rem;
  margin-bottom: 2rem;
  animation: badgeFloat 3s ease-in-out infinite;
}

@keyframes badgeFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.badge-icon {
  font-size: 1.2rem;
}

.badge-text {
  color: white;
  font-weight: 500;
  font-size: 0.9rem;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.title-main {
  display: block;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-sub {
  display: block;
  font-size: 0.7em;
  color: #d4af37;
  margin-top: 0.5rem;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.5rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.subtitle-highlight {
  color: #d4af37;
  font-weight: 600;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: white;
  font-size: 0.9rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 0.75rem 1.5rem;
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.feature-item i {
  color: #d4af37;
  font-size: 1.1rem;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

.btn-glass {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.btn-text {
  margin-right: 0.5rem;
}

.btn-icon {
  transition: transform 0.3s ease;
}

.btn:hover .btn-icon {
  transform: translateX(3px);
}

/* 现代化轮播控制 */
.carousel-controls {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 2rem;
  z-index: 15;
}

.carousel-btn {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 20px;
  color: white;
  opacity: 0.7;
}

.carousel-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
  opacity: 1;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.carousel-btn:active {
  transform: scale(0.95);
}

/* 现代化指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 15;
}

.indicator {
  position: relative;
  width: 40px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 2px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
}

.indicator:hover {
  background: rgba(255, 255, 255, 0.5);
}

.indicator.active {
  background: rgba(255, 255, 255, 0.6);
}

.indicator-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: white;
  border-radius: 2px;
  width: 0;
  transition: width 0.3s ease;
}

.indicator.active .indicator-progress {
  width: 100%;
}

/* 滚动提示 */
.scroll-indicator {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  z-index: 15;
  animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.scroll-arrow {
  font-size: 1.2rem;
  animation: bounce 1s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
}



/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: var(--color-primary, #d4af37);
  color: white;
}

.btn-primary:hover {
  background-color: var(--color-primary-dark, #b8941f);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-primary, #d4af37);
  border: 2px solid var(--color-primary, #d4af37);
}

.btn-outline:hover {
  background-color: var(--color-primary, #d4af37);
  color: white;
}

.btn-outline-white {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-outline-white:hover {
  background-color: white;
  color: var(--color-primary, #d4af37);
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

/* 现代化介绍区域样式 */
.intro-section {
  position: relative;
  padding: 120px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  overflow: hidden;
}

.intro-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.intro-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(212, 175, 55, 0.05) 0%, transparent 50%);
  background-size: 400px 400px;
  animation: patternMove 20s ease-in-out infinite;
}

@keyframes patternMove {
  0%, 100% {
    transform: translate(0, 0);
  }
  50% {
    transform: translate(20px, -20px);
  }
}

.intro-content {
  position: relative;
  z-index: 10;
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.intro-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #d4af37;
  animation: badgeGlow 3s ease-in-out infinite;
}

@keyframes badgeGlow {
  0%, 100% {
    box-shadow: 0 0 0 rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  }
}

.badge-dot {
  width: 8px;
  height: 8px;
  background: #d4af37;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

.section-title {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 800;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.title-highlight {
  display: block;
  color: #d4af37;
  font-size: 0.8em;
  margin-bottom: 0.5rem;
}

.title-main {
  display: block;
  color: #1f2937;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.intro-description {
  max-width: 800px;
  margin: 0 auto 3rem;
}

.intro-text {
  font-size: 1.2rem;
  color: #6b7280;
  line-height: 1.8;
  margin-bottom: 0;
}

.intro-highlights {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin: 4rem 0;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(212, 175, 55, 0.1);
}

.highlight-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(212, 175, 55, 0.3);
}

.highlight-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.highlight-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.highlight-content p {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
}

.intro-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.btn-modern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

.btn-modern i {
  transition: transform 0.3s ease;
}

.btn-modern:hover i {
  transform: translateX(3px);
}

/* 通用区域样式 */
.facilities-section,
.reviews-section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
}

.intro-text {
  font-size: 1.125rem;
  color: #4b5563;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto 40px;
}

.intro-buttons {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 40px;
}

/* 页脚样式 */
.footer {
  background-color: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  font-size: 1.75rem;
  font-weight: bold;
  color: var(--color-primary, #d4af37);
  margin-bottom: 16px;
}

.footer-desc {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 24px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  text-align: center;
  color: #9ca3af;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.admin-entrance-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 0.8rem;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  opacity: 0.6;
}

.admin-entrance-link:hover {
  color: #4caf50;
  background: rgba(76, 175, 80, 0.1);
  opacity: 1;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高级视觉效果 */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(212, 175, 55, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(212, 175, 55, 0.1) 100%
  );
  z-index: 5;
  pointer-events: none;
}

/* 滚动触发动画 */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* 悬停效果增强 */
.facility-card,
.highlight-item {
  position: relative;
  overflow: hidden;
}

.facility-card::before,
.highlight-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
  z-index: 1;
}

.facility-card:hover::before,
.highlight-item:hover::before {
  left: 100%;
}

/* 现代化阴影系统 */
.modern-shadow-sm {
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

.modern-shadow-md {
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.07),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.modern-shadow-lg {
  box-shadow:
    0 10px 15px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.05);
}

.modern-shadow-xl {
  box-shadow:
    0 20px 25px rgba(0, 0, 0, 0.1),
    0 10px 10px rgba(0, 0, 0, 0.04);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-content-wrapper {
    padding: 0 1.5rem;
  }

  .intro-highlights {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 600px;
  }

  .hero-content-wrapper {
    padding: 0 1rem;
  }

  .hero-features {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .btn-large {
    width: 100%;
    max-width: 280px;
  }

  .carousel-controls {
    padding: 0 1rem;
  }

  .carousel-btn {
    width: 50px;
    height: 50px;
    font-size: 18px;
  }

  .intro-section {
    padding: 80px 0;
  }

  .intro-content {
    padding: 0 1rem;
  }

  .intro-highlights {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin: 2rem 0;
  }

  .highlight-item {
    padding: 1rem;
  }

  .highlight-icon {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }

  .intro-buttons {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 500px;
  }

  .hero-badge {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .hero-features {
    display: none;
  }

  .scroll-indicator {
    display: none;
  }

  .carousel-indicators {
    bottom: 1rem;
  }

  .indicator {
    width: 30px;
    height: 3px;
  }

  .intro-badge {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;
  }
}
</style>
