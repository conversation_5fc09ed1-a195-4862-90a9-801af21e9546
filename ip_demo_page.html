<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP助手演示页面 - 普洱蘑菇庄园民宿</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }

        .demo-card:hover {
            transform: translateY(-5px);
        }

        .demo-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .video-demo {
            text-align: center;
            margin-bottom: 20px;
        }

        .demo-video {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .demo-video:hover {
            transform: scale(1.1);
        }

        .demo-video.active {
            border-color: #007bff;
            transform: scale(1.15);
        }

        .chat-demo {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            margin: -20px -20px 20px -20px;
        }

        .quick-nav {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .nav-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .nav-btn:hover {
            background: #e3f2fd;
            border-color: #2196f3;
            transform: translateY(-2px);
        }

        .nav-btn i {
            font-size: 16px;
            color: #007bff;
        }

        .chat-messages {
            max-height: 200px;
            overflow-y: auto;
            padding: 15px;
            background: #fafafa;
            border-radius: 10px;
            margin-bottom: 15px;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .message.ai {
            align-items: flex-start;
        }

        .message.user {
            align-items: flex-end;
        }

        .message-content {
            max-width: 80%;
            padding: 12px 15px;
            border-radius: 15px;
            font-size: 13px;
            line-height: 1.4;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
        }

        .message.user .message-content {
            background: #007bff;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .chat-input {
            display: flex;
            gap: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            outline: none;
            font-size: 13px;
        }

        .chat-input button {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: #007bff;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* IP助手样式 */
        .ip-assistant {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .ip-avatar-container {
            position: relative;
            width: 80px;
            height: 80px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ip-avatar-container:hover {
            transform: scale(1.1);
        }

        .ip-avatar-container.active {
            transform: scale(1.2);
        }

        .ip-avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .status-indicator {
            position: absolute;
            bottom: 5px;
            right: 5px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #28a745;
            border: 2px solid #fff;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-dialog {
            position: absolute;
            bottom: 100px;
            right: 0;
            width: 320px;
            height: 450px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            display: none;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid #e9ecef;
        }

        .chat-dialog.show {
            display: flex;
        }

        .feature-list {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }

        .feature-list h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .feature-item i {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .demo-grid {
                grid-template-columns: 1fr;
            }

            .chat-dialog {
                width: 280px;
                height: 400px;
                bottom: 80px;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 IP助手演示页面</h1>
            <p>体验新的IP形象和智能对话功能</p>
        </div>

        <div class="demo-grid">
            <!-- IP形象演示 -->
            <div class="demo-card">
                <h3>🎬 IP形象视频演示</h3>
                <div class="video-demo">
                    <video 
                        id="video1" 
                        class="demo-video active" 
                        src="/src/assets/images/IP形象/37b48b40dbc80e2a44dce0f626120357_raw.mp4"
                        autoplay 
                        loop 
                        muted 
                        playsinline
                        onclick="switchToVideo(1)"
                    ></video>
                    <video 
                        id="video2" 
                        class="demo-video" 
                        src="/src/assets/images/IP形象/4e8a0e0ab4bdb3d1738dc2f6bf7fb411_raw.mp4"
                        loop 
                        muted 
                        playsinline
                        onclick="switchToVideo(2)"
                    ></video>
                </div>
                <p><strong>左：</strong>默认状态 | <strong>右：</strong>激活状态</p>
                <div>
                    <button class="btn btn-primary" onclick="switchToVideo(1)">默认状态</button>
                    <button class="btn btn-success" onclick="switchToVideo(2)">激活状态</button>
                </div>
            </div>

            <!-- 对话功能演示 -->
            <div class="demo-card">
                <h3>💬 智能对话演示</h3>
                <div class="chat-demo">
                    <div class="chat-header">
                        <div>
                            <h4>🍵 茶茶助手</h4>
                            <small>普洱蘑菇庄园民宿</small>
                        </div>
                        <button onclick="toggleChatDemo()" style="background: none; border: none; color: white; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="quick-nav">
                        <button class="nav-btn" onclick="testQuickNav('房间预订')">
                            <i class="fas fa-bed"></i>
                            房间预订
                        </button>
                        <button class="nav-btn" onclick="testQuickNav('周边产品')">
                            <i class="fas fa-shopping-bag"></i>
                            周边产品
                        </button>
                        <button class="nav-btn" onclick="testQuickNav('茶文化')">
                            <i class="fas fa-leaf"></i>
                            茶文化
                        </button>
                        <button class="nav-btn" onclick="testQuickNav('民宿活动')">
                            <i class="fas fa-calendar-alt"></i>
                            民宿活动
                        </button>
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <div class="message ai">
                            <div class="message-content">
                                🍵 您好！我是茶茶，普洱蘑菇庄园的AI助手！我可以为您介绍茶文化、推荐周边产品和民宿活动。
                            </div>
                        </div>
                    </div>

                    <div class="chat-input">
                        <input type="text" id="chatInput" placeholder="问问茶文化、周边产品或民宿活动..." onkeypress="handleEnter(event)">
                        <button onclick="sendMessage()">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 周边产品演示 -->
            <div class="demo-card">
                <h3>🛍️ 周边产品展示</h3>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 15px;">
                    <img src="/src/assets/images/产品/普洱茶/普洱茶1.jpg" alt="普洱茶" style="width: 100%; height: 80px; object-fit: cover; border-radius: 8px;">
                    <img src="/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg" alt="帆布包" style="width: 100%; height: 80px; object-fit: cover; border-radius: 8px;">
                    <img src="/src/assets/images/IP形象衍生产品/水杯/水杯1.jpg" alt="水杯" style="width: 100%; height: 80px; object-fit: cover; border-radius: 8px;">
                </div>
                <div>
                    <button class="btn btn-primary" onclick="showProducts('茶叶')">茶叶产品</button>
                    <button class="btn btn-success" onclick="showProducts('IP衍生品')">IP衍生品</button>
                    <button class="btn btn-warning" onclick="showProducts('纪念品')">纪念品</button>
                </div>
                <div id="productInfo" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 8px; font-size: 13px;">
                    点击上方按钮查看产品分类
                </div>
            </div>
        </div>

        <div class="feature-list">
            <h3>✨ 功能特色</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <i class="fas fa-video"></i>
                    <h4>动态IP形象</h4>
                    <p>MP4视频形象，状态切换</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-comments"></i>
                    <h4>智能对话</h4>
                    <p>茶文化、产品、活动介绍</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mouse-pointer"></i>
                    <h4>点击切换</h4>
                    <p>点击开关对话框</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-mobile-alt"></i>
                    <h4>响应式设计</h4>
                    <p>适配各种设备</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-shopping-cart"></i>
                    <h4>产品推荐</h4>
                    <p>智能推荐周边产品</p>
                </div>
                <div class="feature-item">
                    <i class="fas fa-leaf"></i>
                    <h4>文化介绍</h4>
                    <p>深度茶文化知识</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模拟IP助手 -->
    <div class="ip-assistant">
        <div class="ip-avatar-container" onclick="toggleIPChat()" id="ipContainer">
            <video 
                id="ipVideo" 
                class="ip-avatar" 
                src="/src/assets/images/IP形象/37b48b40dbc80e2a44dce0f626120357_raw.mp4"
                autoplay 
                loop 
                muted 
                playsinline
            ></video>
            <div class="status-indicator"></div>
        </div>

        <div class="chat-dialog" id="ipChatDialog">
            <div class="chat-header">
                <div>
                    <h4>🍵 茶茶助手</h4>
                    <small>普洱蘑菇庄园民宿</small>
                </div>
                <button onclick="toggleIPChat()" style="background: none; border: none; color: white; cursor: pointer;">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="quick-nav">
                <button class="nav-btn" onclick="ipQuickNav('房间预订')">
                    <i class="fas fa-bed"></i>
                    房间预订
                </button>
                <button class="nav-btn" onclick="ipQuickNav('周边产品')">
                    <i class="fas fa-shopping-bag"></i>
                    周边产品
                </button>
                <button class="nav-btn" onclick="ipQuickNav('茶文化')">
                    <i class="fas fa-leaf"></i>
                    茶文化
                </button>
                <button class="nav-btn" onclick="ipQuickNav('民宿活动')">
                    <i class="fas fa-calendar-alt"></i>
                    民宿活动
                </button>
            </div>

            <div class="chat-messages" id="ipChatMessages">
                <div class="message ai">
                    <div class="message-content">
                        🍵 您好！我是茶茶，普洱蘑菇庄园的AI助手！我可以为您介绍茶文化、推荐周边产品和民宿活动。
                    </div>
                </div>
            </div>

            <div class="chat-input">
                <input type="text" id="ipChatInput" placeholder="问问茶文化、周边产品或民宿活动..." onkeypress="handleIPEnter(event)">
                <button onclick="sendIPMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentVideo = 1;
        let ipChatOpen = false;

        // 视频切换功能
        function switchToVideo(videoNum) {
            const video1 = document.getElementById('video1');
            const video2 = document.getElementById('video2');
            
            if (videoNum === 1) {
                video1.classList.add('active');
                video2.classList.remove('active');
                video1.play();
                video2.pause();
                currentVideo = 1;
            } else {
                video2.classList.add('active');
                video1.classList.remove('active');
                video2.play();
                video1.pause();
                currentVideo = 2;
            }
        }

        // IP助手对话切换
        function toggleIPChat() {
            const dialog = document.getElementById('ipChatDialog');
            const container = document.getElementById('ipContainer');
            const ipVideo = document.getElementById('ipVideo');
            
            ipChatOpen = !ipChatOpen;
            
            if (ipChatOpen) {
                dialog.classList.add('show');
                container.classList.add('active');
                ipVideo.src = '/src/assets/images/IP形象/4e8a0e0ab4bdb3d1738dc2f6bf7fb411_raw.mp4';
            } else {
                dialog.classList.remove('show');
                container.classList.remove('active');
                ipVideo.src = '/src/assets/images/IP形象/37b48b40dbc80e2a44dce0f626120357_raw.mp4';
            }
        }

        // 演示对话功能
        function testQuickNav(nav) {
            addMessage('chatMessages', 'user', nav);
            setTimeout(() => {
                const response = getNavResponse(nav);
                addMessage('chatMessages', 'ai', response);
            }, 500);
        }

        function ipQuickNav(nav) {
            addMessage('ipChatMessages', 'user', nav);
            setTimeout(() => {
                const response = getNavResponse(nav);
                addMessage('ipChatMessages', 'ai', response);
            }, 500);
        }

        function getNavResponse(nav) {
            const responses = {
                '房间预订': '🏠 我可以为您推荐合适的房间！点击"房间预订"按钮，我会为您提供专业的选房服务。',
                '周边产品': '🛍️ 我们有精美的茶叶产品、IP衍生品和纪念品。点击"周边产品"查看完整商品目录！',
                '茶文化': '🍃 普洱茶有着1700多年的历史，是云南特有的地理标志产品。我们提供专业的茶艺体验课程！',
                '民宿活动': '🎉 我们有茶艺体验、蘑菇采摘、自然探索和文化体验等丰富活动。想参加哪个活动呢？'
            };
            return responses[nav] || '感谢您的咨询！';
        }

        function addMessage(containerId, type, content) {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<div class="message-content">${content}</div>`;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (message) {
                addMessage('chatMessages', 'user', message);
                input.value = '';
                setTimeout(() => {
                    const response = generateResponse(message);
                    addMessage('chatMessages', 'ai', response);
                }, 500);
            }
        }

        function sendIPMessage() {
            const input = document.getElementById('ipChatInput');
            const message = input.value.trim();
            if (message) {
                addMessage('ipChatMessages', 'user', message);
                input.value = '';
                setTimeout(() => {
                    const response = generateResponse(message);
                    addMessage('ipChatMessages', 'ai', response);
                }, 500);
            }
        }

        function generateResponse(message) {
            const lowerMessage = message.toLowerCase();
            
            if (lowerMessage.includes('茶') || lowerMessage.includes('普洱')) {
                return '🍃 普洱茶是云南特有的地理标志产品，有着悠久的历史和深厚的文化底蕴。我们庄园提供专业的茶艺体验课程！';
            }
            
            if (lowerMessage.includes('产品') || lowerMessage.includes('购买')) {
                return '🛍️ 我们有精美的茶叶产品、IP衍生品和纪念品。包括普洱茶饼、茶茶主题帆布包、定制水杯等。';
            }
            
            if (lowerMessage.includes('活动') || lowerMessage.includes('体验')) {
                return '🎉 我们有茶艺体验、蘑菇采摘、自然探索和文化体验等活动。每个活动都能让您深度体验云南的生态文化！';
            }
            
            return '🍵 感谢您的咨询！我可以为您介绍茶文化、推荐周边产品或介绍民宿活动。请告诉我您想了解什么！';
        }

        function handleEnter(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function handleIPEnter(event) {
            if (event.key === 'Enter') {
                sendIPMessage();
            }
        }

        function showProducts(category) {
            const info = document.getElementById('productInfo');
            const products = {
                '茶叶': '🍵 茶叶产品：精品普洱茶饼(¥168)、散装普洱茶(¥88)、普洱茶礼盒装(¥298)',
                'IP衍生品': '🌟 IP衍生品：茶茶主题帆布包(¥58)、茶茶纪念挂坠(¥28)、茶茶主题服装(¥128)',
                '纪念品': '🎁 纪念品：庄园纪念勋章(¥38)、茶茶定制水杯(¥88)、平板保护套(¥68)'
            };
            info.innerHTML = products[category] || '产品信息加载中...';
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            switchToVideo(1);
        });
    </script>
</body>
</html>
