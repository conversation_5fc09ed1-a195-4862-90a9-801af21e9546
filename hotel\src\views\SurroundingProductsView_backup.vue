<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 现代化产品页面头部 -->
      <section class="products-hero">
        <div class="hero-background">
          <div class="hero-carousel">
            <div class="hero-slide active" style="background-image: url('/src/assets/images/产品/茶饼/茶饼包装.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/产品/普洱茶/普洱茶摆放环境展示.jpg')">
              <div class="slide-overlay"></div>
            </div>
          </div>
        </div>
        
        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-icon">🛍️</span>
            <span class="badge-text">庄园精选</span>
          </div>
          <h1 class="hero-title">
            <span class="title-main">周边产品</span>
            <span class="title-sub">精选云南特色好物</span>
          </h1>
          <p class="hero-subtitle">
            带走庄园的美好回忆<br>
            <span class="subtitle-highlight">每一件都承载着普洱茶乡的文化底蕴</span>
          </p>
          
          <!-- 产品统计 -->
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">精选产品</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">4.9</div>
              <div class="stat-label">用户评分</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1000+</div>
              <div class="stat-label">满意客户</div>
            </div>
          </div>
          
          <div class="hero-buttons">
            <button @click="scrollToProducts" class="btn btn-primary btn-large btn-glow">
              <span class="btn-text">立即选购</span>
              <span class="btn-icon">🛒</span>
            </button>
            <button @click="scrollToCategories" class="btn btn-outline-white btn-large btn-glass">
              <span class="btn-text">浏览分类</span>
              <span class="btn-icon">↓</span>
            </button>
          </div>
        </div>
        
        <!-- 滚动提示 -->
        <div class="scroll-indicator">
          <div class="scroll-text">探索产品</div>
          <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
          </div>
        </div>
      </section>

      <!-- 分类导航 -->
      <section class="category-section">
        <div class="category-background">
          <div class="category-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">产品分类</h2>
            <p class="section-subtitle">精心分类，便于您的选择</p>
          </div>
          
          <div class="category-grid">
            <div 
              v-for="category in categories" 
              :key="category.id"
              @click="activeCategory = category.id"
              class="category-card"
              :class="{ active: activeCategory === category.id }"
            >
              <div class="category-icon">
                <i :class="category.icon"></i>
              </div>
              <div class="category-content">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-desc">{{ category.description }}</p>
                <div class="category-count">{{ category.count }}+ 产品</div>
              </div>
              <div class="category-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 特色推荐产品 -->
      <section class="featured-section">
        <div class="container">
          <div class="section-header">
            <div class="section-badge">
              <span class="badge-dot"></span>
              <span>精选推荐</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">特色推荐</span>
              <span class="title-main">庄园精选，品质保证</span>
            </h2>
          </div>
          
          <div class="featured-showcase">
            <div 
              v-for="product in featuredProducts" 
              :key="product.id"
              class="featured-card"
              @click="showProductDetail(product)"
            >
              <div class="product-image-container">
                <div class="product-image">
                  <img :src="product.image" :alt="product.name">
                  <div class="image-overlay">
                    <div class="overlay-content">
                      <button class="quick-view-btn">
                        <i class="fas fa-eye"></i>
                        快速查看
                      </button>
                    </div>
                  </div>
                </div>
                <div class="product-badge" :class="getBadgeClass(product.badge)">
                  {{ product.badge }}
                </div>
                <div class="product-rating">
                  <div class="stars">
                    <i v-for="n in 5" :key="n" class="fas fa-star" :class="{ active: n <= (product.rating || 5) }"></i>
                  </div>
                  <span class="rating-text">{{ product.rating || 5 }}</span>
                </div>
              </div>
              
              <div class="product-content">
                <div class="product-category">{{ getCategoryName(product.categoryId) }}</div>
                <h3 class="product-title">{{ product.name }}</h3>
                <p class="product-desc">{{ product.description }}</p>
                
                <div class="product-specs" v-if="product.specs">
                  <span v-for="spec in product.specs.slice(0, 2)" :key="spec" class="spec-tag">
                    {{ spec }}
                  </span>
                </div>
                
                <div class="product-footer">
                  <div class="product-price">
                    <span class="current-price">¥{{ product.price }}</span>
                    <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                    <span v-if="product.originalPrice" class="discount">
                      省¥{{ product.originalPrice - product.price }}
                    </span>
                  </div>
                  <div class="product-actions">
                    <button @click.stop="handleAddToCartClick($event, product)" class="btn-cart">
                      <i class="fas fa-shopping-cart"></i>
                    </button>
                    <button @click.stop="handleBuyNowClick($event, product)" class="btn-buy">
                      立即购买
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 产品展示区域 -->
      <section class="products-section">
        <div class="products-background">
          <div class="products-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">全部产品</h2>
            <div class="products-filter">
              <div class="filter-tabs">
                <button 
                  v-for="category in allCategories" 
                  :key="category.id"
                  @click="activeCategory = category.id"
                  class="filter-tab"
                  :class="{ active: activeCategory === category.id }"
                >
                  <i :class="category.icon"></i>
                  {{ category.name }}
                  <span class="count">{{ getProductCount(category.id) }}</span>
                </button>
              </div>
              <div class="filter-sort">
                <select v-model="sortBy" class="sort-select">
                  <option value="default">默认排序</option>
                  <option value="price-low">价格从低到高</option>
                  <option value="price-high">价格从高到低</option>
                  <option value="rating">评分最高</option>
                  <option value="newest">最新上架</option>
                </select>
              </div>
            </div>
          </div>

          <div class="products-grid">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id"
              class="product-card modern-card"
              @click="showProductDetail(product)"
            >
              <div class="product-image-wrapper">
                <div class="product-image">
                  <img :src="product.image" :alt="product.name" />
                  <div class="image-overlay">
                    <div class="overlay-actions">
                      <button @click.stop="handleQuickView(product)" class="action-btn quick-view">
                        <i class="fas fa-eye"></i>
                        <span class="tooltip">快速查看</span>
                      </button>
                      <button @click.stop="handleAddToCartClick($event, product)" class="action-btn add-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="tooltip">加入购物车</span>
                      </button>
                      <button @click.stop="handleWishlist(product)" class="action-btn wishlist">
                        <i class="fas fa-heart"></i>
                        <span class="tooltip">收藏</span>
                      </button>
                    </div>
                  </div>
                </div>
                
                <!-- 产品标签 -->
                <div v-if="product.badge" class="product-badges">
                  <span class="badge" :class="getBadgeClass(product.badge)">{{ product.badge }}</span>
                  <span v-if="product.isNew" class="badge badge-new">新品</span>
                  <span v-if="product.isHot" class="badge badge-hot">热销</span>
                </div>
                
                <!-- 产品评分 -->
                <div class="product-rating-overlay">
                  <div class="stars">
                    <i v-for="n in 5" :key="n" class="fas fa-star" :class="{ active: n <= (product.rating || 5) }"></i>
                  </div>
                  <span class="rating-score">{{ product.rating || 5 }}</span>
                </div>
              </div>
              
              <div class="product-content">
                <div class="product-category">{{ getCategoryName(product.categoryId) }}</div>
                <h4 class="product-name">{{ product.name }}</h4>
                <p class="product-description">{{ product.description }}</p>
                
                <!-- 产品特性标签 -->
                <div class="product-tags" v-if="product.tags">
                  <span v-for="tag in product.tags.slice(0, 3)" :key="tag" class="tag">
                    {{ tag }}
                  </span>
                </div>
                
                <div class="product-footer">
                  <div class="price-section">
                    <div class="price-main">
                      <span class="current-price">¥{{ product.price }}</span>
                      <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                    </div>
                    <div v-if="product.originalPrice" class="discount-info">
                      <span class="discount-percent">
                        {{ Math.round((1 - product.price / product.originalPrice) * 100) }}% OFF
                      </span>
                    </div>
                  </div>
                  
                  <div class="product-actions">
                    <button @click.stop="handleBuyNowClick($event, product)" class="btn-buy-now">
                      立即购买
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div class="load-more-section" v-if="hasMoreProducts">
            <button @click="loadMoreProducts" class="btn btn-outline btn-large load-more-btn">
              <span v-if="!loading">加载更多产品</span>
              <span v-else>
                <i class="fas fa-spinner fa-spin"></i>
                加载中...
              </span>
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- 购物车组件 -->
    <ShoppingCart />
  </div>
</template>
