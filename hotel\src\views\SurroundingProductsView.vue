<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 现代化产品页面头部 -->
      <section class="products-hero">
        <div class="hero-background">
          <div class="hero-carousel">
            <div class="hero-slide active" style="background-image: url('/src/assets/images/产品/茶饼/茶饼包装.jpg')">
              <div class="slide-overlay"></div>
            </div>
          </div>
        </div>
        
        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-icon">🛍️</span>
            <span class="badge-text">庄园精选</span>
          </div>
          <h1 class="hero-title">
            <span class="title-main">周边产品</span>
            <span class="title-sub">精选云南特色好物</span>
          </h1>
          <p class="hero-subtitle">
            带走庄园的美好回忆<br>
            <span class="subtitle-highlight">每一件都承载着普洱茶乡的文化底蕴</span>
          </p>
          
          <div class="hero-buttons">
            <button @click="scrollToProducts" class="btn btn-primary btn-large">
              <span class="btn-text">立即选购</span>
            </button>
            <button @click="scrollToCategories" class="btn btn-outline-white btn-large">
              <span class="btn-text">浏览分类</span>
            </button>
          </div>
        </div>
      </section>

      <!-- 分类导航 -->
      <section class="category-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">产品分类</h2>
            <p class="section-subtitle">精心分类，便于您的选择</p>
          </div>
          
          <div class="category-grid">
            <div 
              v-for="category in categories" 
              :key="category.id"
              @click="activeCategory = category.id"
              class="category-card"
              :class="{ active: activeCategory === category.id }"
            >
              <div class="category-icon">
                <i :class="category.icon"></i>
              </div>
              <div class="category-content">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-desc">{{ category.description }}</p>
                <div class="category-count">{{ category.count }}+ 产品</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 产品展示区域 -->
      <section class="products-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">全部产品</h2>
          </div>

          <div class="products-grid">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id"
              class="product-card"
              @click="showProductDetail(product)"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name" />
              </div>
              
              <div class="product-content">
                <h4 class="product-name">{{ product.name }}</h4>
                <p class="product-description">{{ product.description }}</p>
                
                <div class="product-footer">
                  <div class="price-section">
                    <span class="current-price">¥{{ product.price }}</span>
                    <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                  </div>
                  
                  <div class="product-actions">
                    <button @click.stop="handleAddToCartClick($event, product)" class="btn-buy-now">
                      加入购物车
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 购物车组件 -->
    <ShoppingCart />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useCartStore } from '../stores/cart'
import AppNavbar from '../components/AppNavbar.vue'
import ShoppingCart from '../components/ShoppingCart.vue'

// 响应式数据
const router = useRouter()
const auth = useAuthStore()
const cartStore = useCartStore()
const activeCategory = ref('all')
const showDetail = ref(false)
const selectedProduct = ref(null)

// 产品分类
const categories = ref([
  {
    id: 'all',
    name: '全部产品',
    icon: 'fas fa-th-large',
    description: '浏览所有庄园产品',
    count: 6
  },
  {
    id: 'tea',
    name: '茶叶产品',
    icon: 'fas fa-leaf',
    description: '精选普洱茶系列',
    count: 3
  },
  {
    id: 'ip',
    name: 'IP衍生品',
    icon: 'fas fa-star',
    description: '茶茶主题周边',
    count: 3
  }
])

// 产品数据
const allProducts = ref([
  {
    id: 1,
    name: '精品普洱茶饼',
    description: '陈年普洱，香醇回甘',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    price: 168,
    originalPrice: 198,
    badge: '热销',
    categoryId: 'tea',
    rating: 5,
    reviews: 234
  },
  {
    id: 2,
    name: '茶茶主题帆布包',
    description: '环保材质，实用美观',
    image: '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
    price: 58,
    badge: '新品',
    categoryId: 'ip',
    rating: 5,
    reviews: 89
  },
  {
    id: 3,
    name: '散装普洱茶',
    description: '传统工艺，原味醇香',
    image: '/src/assets/images/产品/普洱茶/普洱茶摆放环境展示.jpg',
    price: 88,
    originalPrice: 108,
    badge: '推荐',
    categoryId: 'tea',
    rating: 4,
    reviews: 156
  },
  {
    id: 4,
    name: '茶茶玩偶',
    description: '超萌茶茶玩偶，柔软舒适',
    image: '/src/assets/images/IP形象衍生产品/玩偶/玩偶1.jpg',
    price: 128,
    originalPrice: 158,
    badge: '热销',
    categoryId: 'ip',
    rating: 5,
    reviews: 89
  },
  {
    id: 5,
    name: '茶茶主题马克杯',
    description: '精美陶瓷杯，品茶首选',
    image: '/src/assets/images/IP形象衍生产品/马克杯/马克杯1.jpg',
    price: 38,
    badge: '实用',
    categoryId: 'ip',
    rating: 4,
    reviews: 156
  },
  {
    id: 6,
    name: '普洱茶精装礼盒',
    description: '高档包装，送礼佳品',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    price: 288,
    originalPrice: 328,
    badge: '礼品',
    categoryId: 'tea',
    rating: 5,
    reviews: 67
  }
])

// 计算属性
const filteredProducts = computed(() => {
  if (activeCategory.value === 'all') {
    return allProducts.value
  }
  return allProducts.value.filter((product: any) => product.categoryId === activeCategory.value)
})

// 方法
const scrollToProducts = () => {
  const productsSection = document.querySelector('.products-section')
  if (productsSection) {
    productsSection.scrollIntoView({ behavior: 'smooth' })
  }
}

const scrollToCategories = () => {
  const categorySection = document.querySelector('.category-section')
  if (categorySection) {
    categorySection.scrollIntoView({ behavior: 'smooth' })
  }
}

const showProductDetail = (product: any) => {
  selectedProduct.value = product
  showDetail.value = true
}

const handleAddToCartClick = (event: Event, product: any) => {
  event.stopPropagation()
  if (!auth.isAuthenticated) {
    router.push('/login')
    return
  }
  
  try {
    cartStore.addToCart(product, 1)
    alert(`${product.name} 已添加到购物车`)
  } catch (error: any) {
    alert(error.message)
  }
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
/* 基础样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #fafafa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 英雄区域 */
.products-hero {
  position: relative;
  height: 60vh;
  min-height: 500px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  margin-bottom: 2rem;
}

.hero-title {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
}

.title-main {
  display: block;
  color: white;
}

.title-sub {
  display: block;
  font-size: 0.6em;
  color: #d4af37;
  margin-top: 0.5rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.subtitle-highlight {
  color: #d4af37;
  font-weight: 600;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* 分类区域 */
.category-section {
  padding: 80px 0;
  background: white;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.category-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.category-card:hover,
.category-card.active {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #d4af37;
}

.category-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.category-content h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.category-desc {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.category-count {
  color: #d4af37;
  font-weight: 600;
  font-size: 0.9rem;
}

/* 产品区域 */
.products-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
}

.product-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.product-image {
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

.product-content {
  padding: 1.5rem;
}

.product-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.product-description {
  color: #6b7280;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: #d4af37;
}

.original-price {
  font-size: 0.9rem;
  color: #9ca3af;
  text-decoration: line-through;
  margin-left: 0.5rem;
}

.btn-buy-now {
  background: #d4af37;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-buy-now:hover {
  background: #b8941f;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-content {
    padding: 0 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .category-grid {
    grid-template-columns: 1fr;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
}
</style>
