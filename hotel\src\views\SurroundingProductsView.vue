<template>
  <div class="page-container fade-in">
    <AppNavbar />
    <main class="main-content">
      <!-- 现代化产品页面头部 -->
      <section class="products-hero">
        <div class="hero-background">
          <div class="hero-carousel">
            <div class="hero-slide active" style="background-image: url('/src/assets/images/产品/茶饼/茶饼包装.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/产品/普洱茶/普洱茶摆放环境展示.jpg')">
              <div class="slide-overlay"></div>
            </div>
          </div>

          <!-- 装饰性元素 -->
          <div class="hero-decorations">
            <div class="decoration-leaf decoration-leaf-1"></div>
            <div class="decoration-leaf decoration-leaf-2"></div>
            <div class="decoration-tea decoration-tea-1"></div>
          </div>
        </div>

        <div class="hero-content">
          <div class="hero-badge">
            <span class="badge-icon">🛍️</span>
            <span class="badge-text">庄园精选</span>
          </div>
          <h1 class="hero-title">
            <span class="title-main">周边产品</span>
            <span class="title-sub">精选云南特色好物</span>
          </h1>
          <p class="hero-subtitle">
            带走庄园的美好回忆<br>
            <span class="subtitle-highlight">每一件都承载着普洱茶乡的文化底蕴</span>
          </p>

          <!-- 产品统计 -->
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">精选产品</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">4.9</div>
              <div class="stat-label">用户评分</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1000+</div>
              <div class="stat-label">满意客户</div>
            </div>
          </div>

          <div class="hero-buttons">
            <button @click="scrollToProducts" class="btn btn-primary btn-large btn-glow">
              <span class="btn-text">立即选购</span>
              <span class="btn-icon">🛒</span>
            </button>
            <button @click="scrollToCategories" class="btn btn-outline-white btn-large btn-glass">
              <span class="btn-text">浏览分类</span>
              <span class="btn-icon">↓</span>
            </button>
          </div>
        </div>

        <!-- 滚动提示 */
        <div class="scroll-indicator">
          <div class="scroll-text">探索产品</div>
          <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
          </div>
        </div>
      </section>

      <!-- 分类导航 -->
      <section class="category-section">
        <div class="category-background">
          <div class="category-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">产品分类</h2>
            <p class="section-subtitle">精心分类，便于您的选择</p>
          </div>

          <div class="category-grid">
            <div
              v-for="category in categories"
              :key="category.id"
              @click="activeCategory = category.id"
              class="category-card"
              :class="{ active: activeCategory === category.id }"
            >
              <div class="category-icon">
                <i :class="category.icon"></i>
              </div>
              <div class="category-content">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-desc">{{ category.description }}</p>
                <div class="category-count">{{ category.count }}+ 产品</div>
              </div>
              <div class="category-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 特色推荐产品 -->
      <section class="featured-section">
        <div class="container">
          <div class="section-header">
            <div class="section-badge">
              <span class="badge-dot"></span>
              <span>精选推荐</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">特色推荐</span>
              <span class="title-main">庄园精选，品质保证</span>
            </h2>
          </div>

          <div class="featured-showcase">
            <div
              v-for="product in featuredProducts"
              :key="product.id"
              class="featured-card"
              @click="showProductDetail(product)"
            >
              <div class="product-image-container">
                <div class="product-image">
                  <img :src="product.image" :alt="product.name">
                  <div class="image-overlay">
                    <div class="overlay-content">
                      <button class="quick-view-btn">
                        <i class="fas fa-eye"></i>
                        快速查看
                      </button>
                    </div>
                  </div>
                </div>
                <div class="product-badge" :class="getBadgeClass(product.badge)">
                  {{ product.badge }}
                </div>
                <div class="product-rating">
                  <div class="stars">
                    <i v-for="n in 5" :key="n" class="fas fa-star" :class="{ active: n <= (product.rating || 5) }"></i>
                  </div>
                  <span class="rating-text">{{ product.rating || 5 }}</span>
                </div>
              </div>

              <div class="product-content">
                <div class="product-category">{{ getCategoryName(product.categoryId) }}</div>
                <h3 class="product-title">{{ product.name }}</h3>
                <p class="product-desc">{{ product.description }}</p>

                <div class="product-specs" v-if="product.specs">
                  <span v-for="spec in product.specs.slice(0, 2)" :key="spec" class="spec-tag">
                    {{ spec }}
                  </span>
                </div>

                <div class="product-footer">
                  <div class="product-price">
                    <span class="current-price">¥{{ product.price }}</span>
                    <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                    <span v-if="product.originalPrice" class="discount">
                      省¥{{ product.originalPrice - product.price }}
                    </span>
                  </div>
                  <div class="product-actions">
                    <button @click.stop="handleAddToCartClick($event, product)" class="btn-cart">
                      <i class="fas fa-shopping-cart"></i>
                    </button>
                    <button @click.stop="handleBuyNowClick($event, product)" class="btn-buy">
                      立即购买
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 产品展示区域 -->
      <section class="products-section">
        <div class="products-background">
          <div class="products-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">全部产品</h2>
            <div class="products-filter">
              <div class="filter-tabs">
                <button
                  v-for="category in allCategories"
                  :key="category.id"
                  @click="activeCategory = category.id"
                  class="filter-tab"
                  :class="{ active: activeCategory === category.id }"
                >
                  <i :class="category.icon"></i>
                  {{ category.name }}
                  <span class="count">{{ getProductCount(category.id) }}</span>
                </button>
              </div>
              <div class="filter-sort">
                <select v-model="sortBy" class="sort-select">
                  <option value="default">默认排序</option>
                  <option value="price-low">价格从低到高</option>
                  <option value="price-high">价格从高到低</option>
                  <option value="rating">评分最高</option>
                  <option value="newest">最新上架</option>
                </select>
              </div>
            </div>
          </div>

          <div class="products-grid">
            <div
              v-for="product in filteredProducts"
              :key="product.id"
              class="product-card modern-card"
              @click="showProductDetail(product)"
            >
              <div class="product-image-wrapper">
                <div class="product-image">
                  <img :src="product.image" :alt="product.name" />
                  <div class="image-overlay">
                    <div class="overlay-actions">
                      <button @click.stop="handleQuickView(product)" class="action-btn quick-view">
                        <i class="fas fa-eye"></i>
                        <span class="tooltip">快速查看</span>
                      </button>
                      <button @click.stop="handleAddToCartClick($event, product)" class="action-btn add-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="tooltip">加入购物车</span>
                      </button>
                      <button @click.stop="handleWishlist(product)" class="action-btn wishlist">
                        <i class="fas fa-heart"></i>
                        <span class="tooltip">收藏</span>
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 产品标签 -->
                <div v-if="product.badge" class="product-badges">
                  <span class="badge" :class="getBadgeClass(product.badge)">{{ product.badge }}</span>
                  <span v-if="product.isNew" class="badge badge-new">新品</span>
                  <span v-if="product.isHot" class="badge badge-hot">热销</span>
                </div>

                <!-- 产品评分 -->
                <div class="product-rating-overlay">
                  <div class="stars">
                    <i v-for="n in 5" :key="n" class="fas fa-star" :class="{ active: n <= (product.rating || 5) }"></i>
                  </div>
                  <span class="rating-score">{{ product.rating || 5 }}</span>
                </div>
              </div>

              <div class="product-content">
                <div class="product-category">{{ getCategoryName(product.categoryId) }}</div>
                <h4 class="product-name">{{ product.name }}</h4>
                <p class="product-description">{{ product.description }}</p>

                <!-- 产品特性标签 -->
                <div class="product-tags" v-if="product.tags">
                  <span v-for="tag in product.tags.slice(0, 3)" :key="tag" class="tag">
                    {{ tag }}
                  </span>
                </div>

                <div class="product-footer">
                  <div class="price-section">
                    <div class="price-main">
                      <span class="current-price">¥{{ product.price }}</span>
                      <span v-if="product.originalPrice" class="original-price">¥{{ product.originalPrice }}</span>
                    </div>
                    <div v-if="product.originalPrice" class="discount-info">
                      <span class="discount-percent">
                        {{ Math.round((1 - product.price / product.originalPrice) * 100) }}% OFF
                      </span>
                    </div>
                  </div>

                  <div class="product-actions">
                    <button @click.stop="handleBuyNowClick($event, product)" class="btn-buy-now">
                      立即购买
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div class="load-more-section" v-if="hasMoreProducts">
            <button @click="loadMoreProducts" class="btn btn-outline btn-large load-more-btn">
              <span v-if="!loading">加载更多产品</span>
              <span v-else>
                <i class="fas fa-spinner fa-spin"></i>
                加载中...
              </span>
            </button>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-logo">普洱蘑菇庄园</h3>
            <p class="footer-desc">精选云南特色产品，传承自然生态理念</p>
            <div class="social-links">
              <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
              <a href="#" class="social-link"><i class="fab fa-weibo"></i></a>
              <a href="#" class="social-link"><i class="fab fa-wechat"></i></a>
            </div>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">产品分类</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">普洱茶系列</a></li>
              <li><a href="#" class="footer-link">蘑菇制品</a></li>
              <li><a href="#" class="footer-link">手工艺品</a></li>
              <li><a href="#" class="footer-link">生态食品</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">购买服务</h4>
            <ul class="footer-links">
              <li><a href="#" class="footer-link">在线购买</a></li>
              <li><a href="#" class="footer-link">配送说明</a></li>
              <li><a href="#" class="footer-link">退换政策</a></li>
              <li><a href="#" class="footer-link">客户服务</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-title">联系我们</h4>
            <div class="contact-info">
              <div class="contact-item">
                <i class="fas fa-map-marker-alt"></i>
                <span>云南省普洱市</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-phone"></i>
                <span>************</span>
              </div>
              <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2025 普洱蘑菇庄园民宿. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 产品详情弹窗 -->
    <div v-if="showDetail" class="product-detail-modal" @click="closeDetail">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedProduct?.name }}</h3>
          <button @click="closeDetail" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="detail-image">
            <div class="image-gallery" v-if="selectedProduct?.gallery && selectedProduct.gallery.length > 1">
              <img
                v-for="(img, index) in selectedProduct.gallery"
                :key="index"
                :src="img"
                :alt="selectedProduct.name"
                :class="{ active: index === 0 }"
                @click="switchDetailImage($event)"
              />
            </div>
            <img v-else :src="selectedProduct?.image" :alt="selectedProduct?.name" />
          </div>
          <div class="detail-info">
            <p class="detail-desc">{{ selectedProduct?.fullDescription || selectedProduct?.description }}</p>
            <div class="detail-specs" v-if="selectedProduct?.specs">
              <h4>产品规格</h4>
              <ul>
                <li v-for="spec in selectedProduct.specs" :key="spec">{{ spec }}</li>
              </ul>
            </div>
            <div class="detail-rating">
              <div class="stars">
                <i v-for="i in 5" :key="i" class="fas fa-star" :class="{ active: i <= (selectedProduct?.rating || 0) }"></i>
              </div>
              <span class="rating-text">({{ selectedProduct?.reviews || 0 }}条评价)</span>
            </div>
            <div class="detail-price">
              <span class="current-price">¥{{ selectedProduct?.price }}</span>
              <span v-if="selectedProduct?.originalPrice" class="original-price">¥{{ selectedProduct?.originalPrice }}</span>
            </div>
            <div class="detail-actions">
              <button class="btn-primary btn-large">立即购买</button>
              <button @click="handleAddToCartClick($event, selectedProduct)" class="btn-secondary btn-large">加入购物车</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 购物车组件 -->
    <ShoppingCart />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useCartStore } from '../stores/cart'
import AppNavbar from '../components/AppNavbar.vue'
import ShoppingCart from '../components/ShoppingCart.vue'

const router = useRouter()
const auth = useAuthStore()
const cartStore = useCartStore()

// 产品分类
const categories = ref([
  {
    id: 'all',
    name: '全部产品',
    icon: 'fas fa-th-large',
    description: '浏览所有庄园产品',
    count: 50
  },
  {
    id: 'tea',
    name: '茶叶产品',
    icon: 'fas fa-leaf',
    description: '精选普洱茶系列',
    count: 15
  },
  {
    id: 'mushroom',
    name: '蘑菇制品',
    icon: 'fas fa-spa',
    description: '新鲜蘑菇及制品',
    count: 12
  },
  {
    id: 'ip',
    name: 'IP衍生品',
    icon: 'fas fa-star',
    description: '茶茶主题周边',
    count: 18
  },
  {
    id: 'handicraft',
    name: '手工艺品',
    icon: 'fas fa-palette',
    description: '传统手工制作',
    count: 8
  },
  {
    id: 'souvenir',
    name: '纪念品',
    icon: 'fas fa-gift',
    description: '庄园专属纪念',
    count: 10
  }
])

const allCategories = computed(() => categories.value)

const activeCategory = ref('all')

// 响应式数据
const showDetail = ref(false)
const selectedProduct = ref(null)
const sortBy = ref('default')
const loading = ref(false)
const hasMoreProducts = ref(true)
const currentPage = ref(1)
const productsPerPage = 12

// 特色推荐产品
const featuredProducts = ref([
  {
    id: 1,
    name: '精品普洱茶饼',
    description: '选用优质大叶种茶叶，传统工艺制作，茶汤红浓明亮，滋味醇厚回甘',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼包装.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料2.jpg'
    ],
    price: 168,
    originalPrice: 198,
    badge: '热销',
    badgeType: 'hot',
    delay: '0.1s',
    category: 'tea',
    specs: ['重量：357g', '年份：2023年', '产地：云南普洱', '包装：棉纸包装']
  },
  {
    id: 2,
    name: '茶茶主题帆布包',
    description: '以茶茶为主题设计的帆布包，采用优质帆布材料，结实耐用，既实用又具有纪念意义',
    image: '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包2.png',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包3.jpg'
    ],
    price: 58,
    badge: '新品',
    badgeType: 'new',
    delay: '0.2s',
    category: 'ip',
    specs: ['材质：优质帆布', '尺寸：35×40×12cm', '设计：茶茶主题', '容量：15L']
  },
  {
    id: 3,
    name: '庄园纪念勋章（金色版）',
    description: '普洱蘑菇庄园专属纪念勋章，采用精美工艺制作，具有很高的收藏和纪念价值',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg'
    ],
    price: 38,
    badge: '限量',
    badgeType: 'recommend',
    delay: '0.3s',
    category: 'souvenir',
    specs: ['材质：合金镀金', '直径：4cm', '设计：庄园专属', '限量：1000枚']
  }
])

// 茶叶产品数据
const teaProducts = ref([
  {
    id: 1,
    name: '精品普洱茶饼',
    description: '选用优质大叶种茶叶，传统工艺制作',
    fullDescription: '这款精品普洱茶饼选用云南大叶种茶叶，经过传统工艺精心制作。茶汤红浓明亮，滋味醇厚回甘，具有独特的陈香。适合收藏和日常品饮。',
    image: '/src/assets/images/产品/茶饼/茶饼包装.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼包装.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料2.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料3.jpg'
    ],
    price: 168,
    originalPrice: 198,
    badge: '热销',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：357g', '年份：2023年', '产地：云南普洱', '包装：棉纸包装'],
    rating: 5,
    reviews: 89
  },
  {
    id: 2,
    name: '散装普洱茶（一级）',
    description: '新鲜采摘，自然晒干，原汁原味',
    fullDescription: '采用当季新鲜茶叶，经过自然晒干工艺，保持茶叶的原始香味。茶汤清澈，口感清香甘甜，是日常品茶的绝佳选择。',
    image: '/src/assets/images/产品/普洱茶/普洱茶1.jpg',
    gallery: [
      '/src/assets/images/产品/普洱茶/普洱茶1.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶2.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶3.jpg'
    ],
    price: 88,
    originalPrice: 108,
    badge: '新品',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：250g', '等级：一级', '产地：云南普洱', '包装：密封袋装'],
    rating: 4,
    reviews: 156
  },
  {
    id: 3,
    name: '普洱茶精装礼盒',
    description: '精美包装，送礼佳品',
    fullDescription: '精选多款优质普洱茶，配以精美礼盒包装。包含生茶、熟茶各一饼，适合商务送礼或节日馈赠。',
    image: '/src/assets/images/产品/普洱茶/普洱茶包装刀版图.jpg',
    gallery: [
      '/src/assets/images/产品/普洱茶/普洱茶包装刀版图.jpg',
      '/src/assets/images/产品/普洱茶/普洱茶摆放环境展示.jpg'
    ],
    price: 298,
    originalPrice: 358,
    badge: '礼品',
    category: 'tea',
    categoryId: 'tea',
    specs: ['内含：生茶357g + 熟茶357g', '包装：高档礼盒', '产地：云南普洱', '适合：送礼收藏'],
    rating: 5,
    reviews: 67
  },
  {
    id: 4,
    name: '陈年普洱茶饼（5年陈）',
    description: '5年自然陈化，口感醇厚',
    fullDescription: '经过5年自然陈化的普洱茶饼，茶性温和，口感醇厚甘甜。陈香浓郁，汤色红亮，是普洱茶爱好者的珍藏佳品。',
    image: '/src/assets/images/产品/茶饼/茶饼原料4.jpg',
    gallery: [
      '/src/assets/images/产品/茶饼/茶饼原料4.jpg',
      '/src/assets/images/产品/茶饼/茶饼原料1.jpg'
    ],
    price: 388,
    originalPrice: 458,
    badge: '珍藏',
    category: 'tea',
    categoryId: 'tea',
    specs: ['重量：357g', '年份：2019年', '陈化：5年', '产地：云南普洱'],
    rating: 5,
    reviews: 234
  }
])

// IP衍生产品数据
const ipProducts = ref([
  {
    id: 11,
    name: '茶茶主题帆布包',
    description: '可爱IP形象，实用环保',
    fullDescription: '以茶茶为主题设计的帆布包，采用优质帆布材料，结实耐用。包包印有可爱的茶茶形象，既实用又具有纪念意义。',
    image: '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包1.jpg',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包2.png',
      '/src/assets/images/IP形象衍生产品/帆布包/帆布包3.jpg'
    ],
    price: 58,
    badge: '新品',
    category: 'ip',
    categoryId: 'ip',
    specs: ['材质：优质帆布', '尺寸：35×40×12cm', '设计：茶茶主题', '容量：15L'],
    rating: 5,
    reviews: 45,
    tags: ['环保', '实用', 'IP周边'],
    isNew: true
  },
  {
    id: 12,
    name: '茶茶玩偶（大号）',
    description: '超萌茶茶玩偶，柔软舒适',
    fullDescription: '精心制作的茶茶玩偶，采用优质毛绒材料，手感柔软舒适。可爱的造型深受大人小孩喜爱，是完美的陪伴伙伴。',
    image: '/src/assets/images/IP形象衍生产品/玩偶/玩偶1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/玩偶/玩偶1.jpg',
      '/src/assets/images/IP形象衍生产品/玩偶/玩偶2.jpg'
    ],
    price: 128,
    originalPrice: 158,
    badge: '热销',
    category: 'ip',
    categoryId: 'ip',
    specs: ['材质：优质毛绒', '尺寸：30cm', '设计：茶茶形象', '适合：3岁以上'],
    rating: 5,
    reviews: 89,
    tags: ['毛绒玩具', '可爱', 'IP周边'],
    isHot: true
  },
  {
    id: 13,
    name: '茶茶主题马克杯',
    description: '精美陶瓷杯，品茶首选',
    fullDescription: '以茶茶为主题设计的陶瓷马克杯，采用优质陶瓷材料，杯身印有精美的茶茶图案。容量适中，是品茶和日常使用的理想选择。',
    image: '/src/assets/images/IP形象衍生产品/马克杯/马克杯1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/马克杯/马克杯1.jpg',
      '/src/assets/images/IP形象衍生产品/马克杯/马克杯2.jpg'
    ],
    price: 38,
    badge: '实用',
    category: 'ip',
    categoryId: 'ip',
    specs: ['材质：优质陶瓷', '容量：350ml', '设计：茶茶主题', '工艺：高温烧制'],
    rating: 4,
    reviews: 156,
    tags: ['陶瓷', '实用', 'IP周边']
  }
])

// 蘑菇制品数据
const mushroomProducts = ref([
  {
    id: 21,
    name: '新鲜香菇（500g）',
    description: '当日采摘，新鲜美味',
    fullDescription: '庄园自产新鲜香菇，当日采摘当日发货。肉质厚实，香味浓郁，营养丰富，是制作各种美食的优质食材。',
    image: '/src/assets/images/周边产品/蘑菇1.jpg',
    gallery: [
      '/src/assets/images/周边产品/蘑菇1.jpg',
      '/src/assets/images/周边产品/蘑菇2.jpg'
    ],
    price: 28,
    originalPrice: 35,
    badge: '新鲜',
    category: 'mushroom',
    categoryId: 'mushroom',
    specs: ['重量：500g', '产地：庄园自产', '保质期：3天', '储存：冷藏保存'],
    rating: 5,
    reviews: 234,
    tags: ['新鲜', '有机', '营养'],
    isNew: true
  },
  {
    id: 22,
    name: '干制蘑菇片（200g）',
    description: '自然晒干，便于保存',
    fullDescription: '采用传统自然晒干工艺制作的蘑菇片，保持了蘑菇的原始营养和香味。便于长期保存，是煲汤炖菜的绝佳配料。',
    image: '/src/assets/images/周边产品/干蘑菇1.jpg',
    price: 48,
    badge: '传统',
    category: 'mushroom',
    categoryId: 'mushroom',
    specs: ['重量：200g', '工艺：自然晒干', '保质期：12个月', '用途：煲汤炖菜'],
    rating: 4,
    reviews: 89,
    tags: ['干货', '传统工艺', '营养']
  }
])

// 手工艺品数据
const handicraftProducts = ref([
  {
    id: 31,
    name: '手工竹编茶具套装',
    description: '传统竹编工艺，天然环保',
    fullDescription: '采用传统竹编工艺制作的茶具套装，包含茶盘、茶杯垫等。天然竹材，环保健康，展现了云南传统手工艺的精湛技艺。',
    image: '/src/assets/images/周边产品/竹编茶具1.jpg',
    price: 188,
    originalPrice: 228,
    badge: '手工',
    category: 'handicraft',
    categoryId: 'handicraft',
    specs: ['材质：天然竹材', '工艺：传统竹编', '套装：茶盘+杯垫6个', '产地：云南'],
    rating: 5,
    reviews: 45,
    tags: ['手工', '竹编', '环保']
  }
])

// 纪念品数据
const souvenirProducts = ref([
  {
    id: 41,
    name: '庄园纪念勋章（金色版）',
    description: '庄园专属纪念，限量发售',
    fullDescription: '普洱蘑菇庄园专属纪念勋章，采用精美工艺制作。金色版本限量发售，具有很高的收藏和纪念价值。',
    image: '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
    gallery: [
      '/src/assets/images/IP形象衍生产品/勋章/勋章1.jpg',
      '/src/assets/images/IP形象衍生产品/勋章/勋章2.jpg'
    ],
    price: 38,
    badge: '限量',
    category: 'souvenir',
    categoryId: 'souvenir',
    specs: ['材质：合金镀金', '直径：4cm', '设计：庄园专属', '限量：1000枚'],
    rating: 5,
    reviews: 67,
    tags: ['纪念品', '限量', '收藏']
  },
  {
    id: 42,
    name: '庄园明信片套装',
    description: '精美明信片，记录美好回忆',
    fullDescription: '庄园风景明信片套装，包含12张不同主题的精美明信片。记录庄园的四季美景，是分享美好回忆的完美选择。',
    image: '/src/assets/images/周边产品/明信片1.jpg',
    price: 18,
    badge: '经典',
    category: 'souvenir',
    categoryId: 'souvenir',
    specs: ['数量：12张', '尺寸：10×15cm', '材质：优质卡纸', '主题：庄园风景'],
    rating: 4,
    reviews: 123,
    tags: ['明信片', '纪念', '风景']
  }
])


// 合并所有产品数据
const allProducts = computed(() => {
  return [
    ...teaProducts.value,
    ...ipProducts.value,
    ...mushroomProducts.value,
    ...handicraftProducts.value,
    ...souvenirProducts.value
  ]
})

// 过滤产品
const filteredProducts = computed(() => {
  let products = activeCategory.value === 'all'
    ? allProducts.value
    : allProducts.value.filter((product: any) => product.categoryId === activeCategory.value)

  // 排序
  if (sortBy.value === 'price-low') {
    products = products.sort((a: any, b: any) => a.price - b.price)
  } else if (sortBy.value === 'price-high') {
    products = products.sort((a: any, b: any) => b.price - a.price)
  } else if (sortBy.value === 'rating') {
    products = products.sort((a: any, b: any) => (b.rating || 0) - (a.rating || 0))
  }

  return products.slice(0, currentPage.value * productsPerPage)
})

// 获取分类名称
const getCategoryName = (categoryId: string) => {
  const category = categories.value.find(cat => cat.id === categoryId)
  return category ? category.name : '其他'
}

// 获取产品数量
const getProductCount = (categoryId: string) => {
  if (categoryId === 'all') return allProducts.value.length
  return allProducts.value.filter((product: any) => product.categoryId === categoryId).length
}

// 获取徽章样式类
const getBadgeClass = (badge: string) => {
  const badgeMap: { [key: string]: string } = {
    '热销': 'badge-hot',
    '新品': 'badge-new',
    '限量': 'badge-limited',
    '推荐': 'badge-recommend',
    '礼品': 'badge-gift',
    '珍藏': 'badge-premium',
    '传统': 'badge-traditional',
    '手工': 'badge-handmade',
    '经典': 'badge-classic',
    '实用': 'badge-practical',
    '复古': 'badge-vintage',
    '新鲜': 'badge-fresh'
  }
  return badgeMap[badge] || 'badge-default'
}

// 显示产品详情
const showProductDetail = (product: any) => {
  selectedProduct.value = product
  showDetail.value = true
}

// 关闭产品详情
const closeDetail = () => {
  showDetail.value = false
  selectedProduct.value = null
}

// 图片切换功能
const switchImage = (event: Event, productId: number) => {
  const target = event.target as HTMLImageElement
  const container = target.closest('.product-image')
  if (container) {
    // 移除所有active类
    const images = container.querySelectorAll('.image-gallery img')
    const dots = container.querySelectorAll('.gallery-indicator .dot')

    images.forEach((img, index) => {
      img.classList.remove('active')
      if (dots[index]) {
        dots[index].classList.remove('active')
      }
    })

    // 添加active类到当前图片
    target.classList.add('active')
    const imageIndex = Array.from(images).indexOf(target)
    if (dots[imageIndex]) {
      dots[imageIndex].classList.add('active')
    }
  }
}

// 详情图片切换功能
const switchDetailImage = (event: Event) => {
  const target = event.target as HTMLImageElement
  const container = target.closest('.image-gallery')
  if (container) {
    const images = container.querySelectorAll('img')
    images.forEach(img => img.classList.remove('active'))
    target.classList.add('active')
  }
}

// 登录检查函数
const requireLogin = () => {
  if (!auth.isAuthenticated) {
    router.push('/login')
    return false
  }
  return true
}

// 滚动到产品区域
const scrollToProducts = () => {
  const productsSection = document.querySelector('.products-section')
  if (productsSection) {
    productsSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// 滚动到分类区域
const scrollToCategories = () => {
  const categorySection = document.querySelector('.category-section')
  if (categorySection) {
    categorySection.scrollIntoView({ behavior: 'smooth' })
  }
}

// 处理快速查看
const handleQuickView = (product: any) => {
  showProductDetail(product)
}

// 处理收藏
const handleWishlist = (product: any) => {
  if (!requireLogin()) return
  // 收藏功能
  showSuccessMessage(`${product.name} 已添加到收藏夹`)
}

// 处理立即购买
const handleBuyNowClick = (event?: Event, product?: any) => {
  if (event) event.stopPropagation()
  if (!requireLogin()) return

  const targetProduct = product || selectedProduct.value
  if (!targetProduct) {
    alert('请选择要购买的产品')
    return
  }

  // 添加到购物车并跳转到结算页面
  try {
    cartStore.addToCart(targetProduct, 1)
    // 这里可以直接触发购物车的结算流程
    showSuccessMessage(`${targetProduct.name} 已添加到购物车，准备结算`)
  } catch (error: any) {
    alert(error.message)
  }
}

// 加载更多产品
const loadMoreProducts = () => {
  loading.value = true
  setTimeout(() => {
    currentPage.value++
    loading.value = false
    if (currentPage.value * productsPerPage >= allProducts.value.length) {
      hasMoreProducts.value = false
    }
  }, 1000)
}

// 处理加入购物车点击
const handleAddToCartClick = (event?: Event, product?: any) => {
  if (event) event.stopPropagation()
  if (!requireLogin()) return

  // 如果没有传入产品，使用当前选中的产品
  const targetProduct = product || selectedProduct.value
  if (!targetProduct) {
    alert('请选择要添加的产品')
    return
  }

  try {
    cartStore.addToCart(targetProduct, 1)
    showSuccessMessage(`${targetProduct.name} 已添加到购物车`)
  } catch (error: any) {
    alert(error.message)
  }
}

// 显示成功消息
const showSuccessMessage = (message: string) => {
  const toast = document.createElement('div')
  toast.textContent = message
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    z-index: 9999;
    font-size: 14px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    animation: slideIn 0.3s ease;
  `

  const style = document.createElement('style')
  style.textContent = `
    @keyframes slideIn {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
  `
  document.head.appendChild(style)
  document.body.appendChild(toast)

  setTimeout(() => {
    toast.remove()
    style.remove()
  }, 3000)
}

// 处理快速查看点击
const handleQuickViewClick = () => {
  if (!requireLogin()) return
  // 已登录，可以查看详情
  alert('快速查看功能开发中...')
}

// 处理查看更多点击
const handleViewMoreClick = () => {
  if (!requireLogin()) return
  // 已登录，可以查看更多产品
  alert('查看更多产品功能开发中...')
}
</script>

<style scoped>
/* 现代化产品页面样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #fafafa;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 现代化英雄区域 */
.products-hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-carousel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 2;
}

/* 英雄内容区域 */
.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.badge-icon {
  font-size: 1.2rem;
}

.badge-text {
  color: white;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.title-main {
  display: block;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.title-sub {
  display: block;
  font-size: 0.6em;
  color: #d4af37;
  margin-top: 0.5rem;
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.subtitle-highlight {
  color: #d4af37;
  font-weight: 600;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin: 2.5rem 0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #d4af37;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.btn-glow {
  position: relative;
  overflow: hidden;
}

.btn-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-glow:hover::before {
  left: 100%;
}

.btn-glass {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  z-index: 15;
  animation: scrollPulse 2s ease-in-out infinite;
}

@keyframes scrollPulse {
  0%, 100% {
    opacity: 0.7;
    transform: translateX(-50%) translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
  }
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.scroll-arrow {
  font-size: 1.2rem;
  animation: bounce 1s ease-in-out infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(5px);
  }
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background-color: #d4af37;
  color: white;
}

.btn-primary:hover {
  background-color: #b8941f;
}

.btn-outline {
  background-color: transparent;
  color: #d4af37;
  border: 2px solid #d4af37;
}

.btn-outline:hover {
  background-color: #d4af37;
  color: white;
}

.btn-outline-white {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn-outline-white:hover {
  background-color: white;
  color: #d4af37;
}

.btn-large {
  padding: 16px 32px;
  font-size: 18px;
}

/* 分类导航 */
.category-section {
  padding: 40px 0;
  background-color: #f9fafb;
}

.category-nav {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.category-btn {
  padding: 12px 24px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  color: #6b7280;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.category-btn:hover,
.category-btn.active {
  background: #d4af37;
  border-color: #d4af37;
  color: white;
  transform: translateY(-2px);
}

/* 通用区域样式 */
.featured-section,
.products-section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 3rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
}

/* 特色产品网格 */
.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  margin-bottom: 40px;
}

.featured-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.featured-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: white;
}

.product-badge.hot {
  background-color: #d4af37;
}

.product-badge.new {
  background-color: #10b981;
}

.product-badge.recommend {
  background-color: #f59e0b;
}

.product-content {
  padding: 24px;
}

.product-title {
  font-size: 1.25rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 12px;
}

.product-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #d4af37;
}

.original-price {
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
}

.product-btn {
  background: #d4af37;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.product-btn:hover {
  background: #b8941f;
  transform: translateY(-1px);
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.product-card .product-image {
  position: relative;
  height: 200px;
}

.product-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
  opacity: 1;
}

.quick-view-btn {
  background: white;
  color: #1f2937;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.quick-view-btn:hover {
  background: #d4af37;
  color: white;
}

.product-info {
  padding: 16px;
}

.product-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.product-category {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 8px;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.stars {
  display: flex;
  gap: 2px;
}

.stars i {
  color: #e5e7eb;
  font-size: 0.875rem;
}

.stars i.active {
  color: #d4af37;
}

.rating-text {
  color: #6b7280;
  font-size: 0.875rem;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 1.25rem;
  font-weight: bold;
  color: #d4af37;
}

.add-cart-btn {
  width: 32px;
  height: 32px;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-cart-btn:hover {
  background: #b8941f;
  transform: scale(1.1);
}

.section-footer {
  text-align: center;
}

/* 页脚样式 */
.footer {
  background-color: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-logo {
  font-size: 1.75rem;
  font-weight: bold;
  color: #d4af37;
  margin-bottom: 16px;
  font-family: 'Pacifico', cursive;
}

.footer-desc {
  color: #d1d5db;
  line-height: 1.6;
  margin-bottom: 24px;
}

.social-links {
  display: flex;
  gap: 16px;
}

.social-link {
  color: #d1d5db;
  font-size: 1.25rem;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: #d4af37;
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #d4af37;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #d1d5db;
}

.contact-item i {
  color: #d4af37;
  width: 20px;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  text-align: center;
  color: #9ca3af;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

.slide-in-up {
  animation: slideInUp 0.8s ease-out;
  animation-fill-mode: both;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .featured-grid,
  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .category-nav {
    gap: 12px;
  }

  .category-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 32px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .featured-grid,
  .products-grid {
    gap: 16px;
  }
}

/* 图片轮播样式 */
.image-gallery {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-gallery img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.3s ease;
  cursor: pointer;
}

.image-gallery img.active {
  opacity: 1;
}

.image-gallery img:hover {
  transform: scale(1.05);
}

.single-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .single-image {
  transform: scale(1.05);
}

.gallery-indicator {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 5px;
}

.gallery-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.gallery-indicator .dot.active {
  background: white;
}

.product-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
}

.product-badge.hot {
  background: #ff6b6b;
}

.product-badge.new {
  background: #4ecdc4;
}

.product-badge.recommend {
  background: #45b7d1;
}

/* 价格显示优化 */
.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.current-price {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e74c3c;
}

.original-price {
  font-size: 0.9rem;
  text-decoration: line-through;
  color: #999;
  margin-top: 2px;
}

/* 产品详情弹窗 */
.product-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 20px;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  margin: 20px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  padding: 30px;
}

.detail-image {
  position: relative;
}

.detail-image .image-gallery {
  height: 300px;
  border-radius: 10px;
  overflow: hidden;
}

.detail-image .image-gallery img {
  cursor: pointer;
  border-radius: 10px;
}

.detail-image img:not(.image-gallery img) {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 10px;
}

.detail-desc {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detail-specs h4 {
  margin-bottom: 10px;
  color: #333;
}

.detail-specs ul {
  list-style: none;
  padding: 0;
  margin-bottom: 20px;
}

.detail-specs li {
  padding: 5px 0;
  color: #666;
  border-bottom: 1px solid #f0f0f0;
}

.detail-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.detail-rating .stars {
  display: flex;
  gap: 2px;
}

.detail-rating .stars i {
  color: #ddd;
  font-size: 14px;
}

.detail-rating .stars i.active {
  color: #ffc107;
}

.detail-rating .rating-text {
  color: #666;
  font-size: 14px;
}

.detail-price {
  margin-bottom: 30px;
}

.detail-price .current-price {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e74c3c;
}

.detail-price .original-price {
  margin-left: 10px;
  font-size: 1.2rem;
  text-decoration: line-through;
  color: #999;
}

.detail-actions {
  display: flex;
  gap: 15px;
}

.btn-large {
  padding: 15px 30px;
  font-size: 1.1rem;
}

.btn-primary {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #e9ecef;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-body {
    grid-template-columns: 1fr;
  }

  .detail-actions {
    flex-direction: column;
  }
}
</style>
