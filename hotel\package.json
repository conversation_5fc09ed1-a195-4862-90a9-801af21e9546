{"name": "hotel", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "proxy": "node proxy-server.js", "install-proxy": "npm install express http-proxy-middleware cors node-fetch@3.3.2"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "aos": "^2.3.4", "axios": "^1.10.0", "cors": "^2.8.5", "element-plus": "^2.10.3", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "node-fetch": "^3.3.2", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-cli-plugin-element-plus": "^0.0.12", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/aos": "^3.0.7", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^8.0.4", "postcss": "^8.5.6", "typescript": "~5.8.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}