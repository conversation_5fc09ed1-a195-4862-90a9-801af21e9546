<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify AI 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        input[type="text"] {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .log {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            background: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Dify AI 服务调试测试</h1>
        
        <div class="test-section">
            <h3>📡 连接测试</h3>
            <button onclick="testConnection()">测试 Dify 服务连接</button>
            <div id="connectionResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💬 聊天测试</h3>
            <input type="text" id="chatInput" placeholder="输入测试消息..." value="推荐房间">
            <button onclick="testChat()">发送消息</button>
            <div id="chatResult" class="response" style="display: none;"></div>
            <div id="chatLog" class="log" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔄 流式响应测试</h3>
            <input type="text" id="streamInput" placeholder="输入测试消息..." value="有没有2楼的房间">
            <button onclick="testStream()">测试流式响应</button>
            <div id="streamResult" class="response" style="display: none;"></div>
            <div id="streamLog" class="log" style="display: none;"></div>
        </div>
    </div>

    <script>
        const DIFY_BASE_URL = 'http://4295a4ce.r28.cpolar.top/v1';
        const DIFY_TOKEN = 'app-oaUwvb7k2zbC8Bi03EO977nN';

        function log(elementId, message) {
            const logElement = document.getElementById(elementId);
            logElement.style.display = 'block';
            logElement.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }

        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response ' + (isError ? 'error' : 'success');
            element.textContent = content;
        }

        async function testConnection() {
            const resultElement = document.getElementById('connectionResult');
            
            try {
                showResult('connectionResult', '正在测试连接...', false);
                
                const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
                    method: 'OPTIONS',
                    headers: {
                        'Authorization': `Bearer ${DIFY_TOKEN}`,
                    }
                });

                if (response.ok || response.status === 405) {
                    showResult('connectionResult', '✅ Dify 服务连接正常！', false);
                } else {
                    showResult('connectionResult', `❌ 连接失败: ${response.status} ${response.statusText}`, true);
                }
            } catch (error) {
                showResult('connectionResult', `❌ 连接错误: ${error.message}`, true);
            }
        }

        async function testChat() {
            const input = document.getElementById('chatInput').value;
            if (!input.trim()) {
                alert('请输入测试消息');
                return;
            }

            log('chatLog', '开始聊天测试...');
            log('chatLog', `发送消息: ${input}`);

            try {
                const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${DIFY_TOKEN}`,
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: input,
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user'
                    })
                });

                log('chatLog', `响应状态: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    log('chatLog', `收到响应: ${JSON.stringify(data, null, 2)}`);
                    
                    if (data.answer) {
                        showResult('chatResult', data.answer, false);
                    } else {
                        showResult('chatResult', '收到响应但没有 answer 字段: ' + JSON.stringify(data), true);
                    }
                } else {
                    const errorText = await response.text();
                    log('chatLog', `错误响应: ${errorText}`);
                    showResult('chatResult', `请求失败: ${response.status} ${errorText}`, true);
                }
            } catch (error) {
                log('chatLog', `异常: ${error.message}`);
                showResult('chatResult', `请求异常: ${error.message}`, true);
            }
        }

        async function testStream() {
            const input = document.getElementById('streamInput').value;
            if (!input.trim()) {
                alert('请输入测试消息');
                return;
            }

            log('streamLog', '开始流式响应测试...');
            log('streamLog', `发送消息: ${input}`);

            try {
                const response = await fetch(`${DIFY_BASE_URL}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${DIFY_TOKEN}`,
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: input,
                        response_mode: 'streaming',
                        conversation_id: '',
                        user: 'test-user'
                    })
                });

                log('streamLog', `响应状态: ${response.status}`);

                if (response.ok) {
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let fullResponse = '';

                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) break;

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.trim() === '') continue;
                            
                            log('streamLog', `收到行: ${line}`);
                            
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                
                                if (data === '[DONE]') {
                                    log('streamLog', '流式响应结束');
                                    break;
                                }

                                try {
                                    const parsed = JSON.parse(data);
                                    log('streamLog', `解析数据: ${JSON.stringify(parsed)}`);
                                    
                                    if (parsed.answer) {
                                        fullResponse += parsed.answer;
                                        showResult('streamResult', fullResponse, false);
                                    }
                                } catch (e) {
                                    log('streamLog', `JSON解析失败: ${e.message}, 原始数据: ${data}`);
                                    fullResponse += data;
                                    showResult('streamResult', fullResponse, false);
                                }
                            }
                        }
                    }

                    if (!fullResponse) {
                        showResult('streamResult', '没有收到有效的响应内容', true);
                    }
                } else {
                    const errorText = await response.text();
                    log('streamLog', `错误响应: ${errorText}`);
                    showResult('streamResult', `请求失败: ${response.status} ${errorText}`, true);
                }
            } catch (error) {
                log('streamLog', `异常: ${error.message}`);
                showResult('streamResult', `请求异常: ${error.message}`, true);
            }
        }
    </script>
</body>
</html>
