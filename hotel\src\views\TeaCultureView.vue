<template>
  <div class="page-container">
    <AppNavbar />

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 茶文化英雄区域 -->
      <section class="hero-section">
        <div class="hero-background">
          <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研10.jpg" alt="普洱茶园">
          <div class="hero-overlay"></div>
        </div>
        <div class="hero-content">
          <div class="container">
            <h1 class="hero-title">普洱茶文化</h1>
            <p class="hero-subtitle">千年茶韵，品味云南茶乡的深厚文化底蕴</p>
            <div class="hero-buttons">
              <button @click="scrollToSection('tea-history')" class="btn btn-primary btn-large">探索茶史</button>
              <button @click="scrollToSection('tea-experience')" class="btn btn-outline btn-large">体验茶艺</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 普洱茶历史区域 -->
      <section id="tea-history" class="tea-history-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">普洱茶的历史传承</h2>
            <p class="section-subtitle">追溯千年茶马古道，感受普洱茶的深厚文化底蕴</p>
          </div>

          <div class="history-timeline">
            <div class="timeline-item">
              <div class="timeline-marker">
                <i class="fas fa-leaf"></i>
              </div>
              <div class="timeline-content">
                <h3 class="timeline-title">东汉时期</h3>
                <p class="timeline-desc">普洱茶的起源可追溯到东汉时期，当时云南地区的先民就开始种植和饮用茶叶。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-marker">
                <i class="fas fa-mountain"></i>
              </div>
              <div class="timeline-content">
                <h3 class="timeline-title">唐宋时期</h3>
                <p class="timeline-desc">茶马古道兴起，普洱茶开始通过马帮运输到西藏、四川等地，成为重要的贸易商品。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-marker">
                <i class="fas fa-crown"></i>
              </div>
              <div class="timeline-content">
                <h3 class="timeline-title">清朝时期</h3>
                <p class="timeline-desc">普洱茶被列为贡茶，享有"茶中之王"的美誉，制茶工艺日趋完善。</p>
              </div>
            </div>

            <div class="timeline-item">
              <div class="timeline-marker">
                <i class="fas fa-globe"></i>
              </div>
              <div class="timeline-content">
                <h3 class="timeline-title">现代时期</h3>
                <p class="timeline-desc">普洱茶走向世界，成为中国茶文化的重要代表，深受国内外茶友喜爱。</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 茶园介绍区域 -->
      <section class="tea-garden-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">庄园茶园</h2>
            <p class="section-subtitle">走进我们的生态茶园，感受大自然的馈赠</p>
          </div>

          <div class="garden-grid">
            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研15.jpg" alt="古茶树园">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">千年古茶树园</h3>
                <p class="garden-desc">拥有300多年历史的古茶树群落，每一片茶叶都承载着岁月的沉淀。</p>
                <div class="garden-features">
                  <span class="feature-tag">有机种植</span>
                  <span class="feature-tag">古树茶</span>
                  <span class="feature-tag">生态环保</span>
                </div>
              </div>
            </div>

            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研8.jpg" alt="生态茶园">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">生态有机茶园</h3>
                <p class="garden-desc">采用现代生态种植技术，确保每一片茶叶的纯净与健康。</p>
                <div class="garden-features">
                  <span class="feature-tag">无农药</span>
                  <span class="feature-tag">纯天然</span>
                  <span class="feature-tag">可持续</span>
                </div>
              </div>
            </div>

            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品制作工具.jpg" alt="茶叶加工">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">传统制茶工坊</h3>
                <p class="garden-desc">传承古法制茶工艺，结合现代技术，打造高品质普洱茶。</p>
                <div class="garden-features">
                  <span class="feature-tag">传统工艺</span>
                  <span class="feature-tag">手工制作</span>
                  <span class="feature-tag">品质保证</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- AI智能茶艺体验区域 -->
      <section id="ai-tea-experience" class="ai-tea-experience-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">AI智能茶艺体验</h2>
            <p class="section-subtitle">结合人工智能技术，为您提供个性化的茶文化学习体验</p>
          </div>

          <div class="ai-features-grid">
            <div class="ai-feature-card" @click="openAIAssistant('recognition')">
              <div class="feature-icon">
                <i class="fas fa-camera"></i>
              </div>
              <h3>AI茶叶识别</h3>
              <p>上传茶叶照片，AI智能识别茶叶品种、产地、品质等信息</p>
              <div class="feature-highlight">
                <span class="highlight-tag">智能识别</span>
                <span class="highlight-tag">文化解读</span>
              </div>
            </div>

            <div class="ai-feature-card" @click="openAIAssistant('brewing')">
              <div class="feature-icon">
                <i class="fas fa-coffee"></i>
              </div>
              <h3>智能冲泡指导</h3>
              <p>根据茶叶类型和个人喜好，提供专业的冲泡步骤和技巧指导</p>
              <div class="feature-highlight">
                <span class="highlight-tag">个性化</span>
                <span class="highlight-tag">专业指导</span>
              </div>
            </div>

            <div class="ai-feature-card" @click="openAIAssistant('knowledge')">
              <div class="feature-icon">
                <i class="fas fa-book"></i>
              </div>
              <h3>茶文化知识库</h3>
              <p>深度学习普洱茶历史、制作工艺、文化传统等丰富知识</p>
              <div class="feature-highlight">
                <span class="highlight-tag">深度学习</span>
                <span class="highlight-tag">文化传承</span>
              </div>
            </div>

            <div class="ai-feature-card" @click="openAIAssistant('chat')">
              <div class="feature-icon">
                <i class="fas fa-comments"></i>
              </div>
              <h3>AI茶文化顾问</h3>
              <p>24小时在线的智能茶文化顾问，解答您的所有茶文化疑问</p>
              <div class="feature-highlight">
                <span class="highlight-tag">24小时在线</span>
                <span class="highlight-tag">专业解答</span>
              </div>
            </div>
          </div>

          <div class="ai-demo-section">
            <div class="demo-content">
              <div class="demo-text">
                <h3>体验AI茶文化助手</h3>
                <p>我们的AI茶文化助手融合了深度学习技术和传统茶文化知识，能够为您提供：</p>
                <ul>
                  <li>🔍 精准的茶叶品种识别和品质评估</li>
                  <li>📚 丰富的茶文化历史和制作工艺知识</li>
                  <li>☕ 个性化的冲泡建议和技巧指导</li>
                  <li>💬 智能对话和实时问答服务</li>
                </ul>
                <button @click="openAIAssistant('chat')" class="demo-btn">
                  <i class="fas fa-robot"></i>
                  立即体验AI助手
                </button>
              </div>
              <div class="demo-visual">
                <div class="ai-avatar-large">
                  <div class="avatar-circle">
                    <div class="tea-leaf-large">🍃</div>
                    <div class="ai-pulse"></div>
                  </div>
                  <div class="ai-name">茶文化AI助手</div>
                  <div class="ai-status-text">在线服务中</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 传统茶艺体验区域 -->
      <section id="tea-experience" class="tea-experience-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">传统茶艺体验</h2>
            <p class="section-subtitle">在专业茶艺师的指导下，体验正宗的普洱茶文化</p>
          </div>

          <div class="experience-grid">
            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-leaf"></i>
              </div>
              <h3 class="experience-title">茶叶品鉴</h3>
              <p class="experience-desc">学习识别不同年份、不同产区的普洱茶，掌握品茶的基本技巧。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：2小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：2-8人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥188/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>

            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-hands"></i>
              </div>
              <h3 class="experience-title">制茶工艺</h3>
              <p class="experience-desc">亲手参与普洱茶的制作过程，从采摘到压制，体验传统制茶工艺。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：4小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：4-12人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥388/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>

            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-meditation"></i>
              </div>
              <h3 class="experience-title">茶道禅修</h3>
              <p class="experience-desc">在宁静的茶室中，通过茶道修身养性，感受内心的平静与和谐。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：3小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：1-6人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥288/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 茶文化知识区域 -->
      <section class="tea-knowledge-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">茶文化知识</h2>
            <p class="section-subtitle">深入了解普洱茶的分类、功效和冲泡技巧</p>
          </div>

          <div class="knowledge-tabs">
            <div class="tab-buttons">
              <button
                v-for="tab in knowledgeTabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="['tab-btn', { active: activeTab === tab.id }]"
              >
                {{ tab.title }}
              </button>
            </div>

            <div class="tab-content">
              <div v-if="activeTab === 'classification'" class="tab-panel">
                <div class="classification-grid">
                  <div class="classification-item">
                    <h4>生茶</h4>
                    <p>未经发酵的普洱茶，茶性较烈，适合长期收藏。</p>
                  </div>
                  <div class="classification-item">
                    <h4>熟茶</h4>
                    <p>经过人工发酵的普洱茶，茶性温和，适合日常饮用。</p>
                  </div>
                  <div class="classification-item">
                    <h4>古树茶</h4>
                    <p>来自百年以上古茶树的茶叶，品质上乘，口感独特。</p>
                  </div>
                  <div class="classification-item">
                    <h4>台地茶</h4>
                    <p>来自人工种植茶园的茶叶，产量较大，价格适中。</p>
                  </div>
                </div>
              </div>

              <div v-if="activeTab === 'benefits'" class="tab-panel">
                <div class="benefits-list">
                  <div class="benefit-item">
                    <i class="fas fa-heart"></i>
                    <div>
                      <h4>降脂减肥</h4>
                      <p>普洱茶含有丰富的茶多酚，有助于降低血脂，促进新陈代谢。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-shield-alt"></i>
                    <div>
                      <h4>抗氧化</h4>
                      <p>茶叶中的抗氧化成分有助于延缓衰老，增强免疫力。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-brain"></i>
                    <div>
                      <h4>提神醒脑</h4>
                      <p>适量的咖啡因有助于提高注意力，缓解疲劳。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-stomach"></i>
                    <div>
                      <h4>养胃护胃</h4>
                      <p>熟普洱茶性温和，有助于保护胃黏膜，促进消化。</p>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="activeTab === 'brewing'" class="tab-panel">
                <div class="brewing-steps">
                  <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h4>温杯洁具</h4>
                      <p>用热水冲洗茶具，提高茶具温度，去除异味。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h4>投茶醒茶</h4>
                      <p>按1:20的比例投茶，用热水快速冲洗茶叶，倒掉第一泡。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h4>冲泡品饮</h4>
                      <p>用95-100°C的热水冲泡，第一泡30秒，后续每泡延长10秒。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h4>品味回甘</h4>
                      <p>细品茶汤的香气、滋味和回甘，感受普洱茶的独特魅力。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>普洱蘑菇庄园</h3>
            <p>隐于森林深处，与自然共生的生态民宿体验</p>
          </div>
          <div class="footer-section">
            <h4>联系我们</h4>
            <p>地址：云南省普洱市思茅区</p>
            <p>电话：400-123-4567</p>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="footer-section">
            <h4>快速链接</h4>
            <router-link to="/" class="footer-link">首页</router-link>
            <router-link to="/tea-culture" class="footer-link">茶文化</router-link>
            <router-link to="/products" class="footer-link">民宿产品</router-link>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 普洱蘑菇庄园民宿. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 全局IP助手 -->
    <!-- <GlobalIPAssistant /> -->
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useRouter } from 'vue-router'
import AppNavbar from '../components/AppNavbar.vue'
// import GlobalIPAssistant from '../components/GlobalIPAssistant.vue'
import { teaCultureData } from '../data/mockData'
// import TeaAIAssistant from '../components/TeaAIAssistant.vue'

const auth = useAuthStore()
const router = useRouter()

// 茶文化数据
const teaVarieties = ref(teaCultureData.teaVarieties)
const teaExperiences = ref(teaCultureData.teaExperiences)
const teaKnowledge = ref(teaCultureData.teaKnowledge)

// 当前激活的标签页
const activeTab = ref('classification')

// 知识标签页数据
const knowledgeTabs = [
  { id: 'classification', title: '茶叶分类' },
  { id: 'benefits', title: '功效作用' },
  { id: 'brewing', title: '冲泡技巧' }
]

// 滚动到指定区域
const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 打开AI助手并切换到指定功能
const openAIAssistant = (tab: string) => {
  // 这里可以通过事件或者状态管理来控制AI助手的打开和切换
  // 暂时使用简单的方式，直接滚动到页面底部让用户看到AI助手
  window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })

  // 可以在这里添加更多逻辑，比如：
  // - 发送事件给AI助手组件
  // - 设置全局状态
  // - 显示提示信息等
  setTimeout(() => {
    // 模拟点击AI助手按钮
    const aiTrigger = document.querySelector('.ai-trigger') as HTMLElement
    if (aiTrigger) {
      aiTrigger.click()
    }
  }, 500)
}

// 处理预约按钮点击
const handleBookingClick = () => {
  if (!auth.isAuthenticated) {
    // 未登录，跳转到登录页面
    router.push('/login')
    return
  }

  // 已登录，跳转到AI选房页面
  router.push('/ai-rooms')
}
</script>

<style scoped>
/* 基础样式 */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 导航栏样式 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(212, 175, 55, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #d4af37;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-menu {
  display: flex;
  gap: 32px;
}

.nav-link {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: #d4af37;
  background: rgba(212, 175, 55, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 12px;
}

.user-info {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: #d4af37;
  color: white;
}

.btn-primary:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #d4af37;
  border: 2px solid #d4af37;
}

.btn-outline:hover {
  background: #d4af37;
  color: white;
}

.btn-large {
  padding: 14px 28px;
  font-size: 1.1rem;
}

/* 主要内容区域 */
.main-content {
  padding-top: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 英雄区域样式 */
.hero-section {
  position: relative;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 32px;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 区域通用样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* 茶历史时间线样式 */
.tea-history-section {
  padding: 100px 0;
  background: white;
}

.history-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #d4af37, #b8941f);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-marker {
  width: 60px;
  height: 60px;
  background: #d4af37;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  position: relative;
  z-index: 2;
  margin: 0 40px;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.timeline-content {
  flex: 1;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.timeline-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #d4af37;
  margin-bottom: 12px;
}

.timeline-desc {
  color: #6b7280;
  line-height: 1.6;
}

/* 茶园介绍样式 */
.tea-garden-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.garden-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.garden-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.garden-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.garden-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.garden-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.garden-card:hover .garden-image img {
  transform: scale(1.05);
}

.garden-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.garden-content {
  padding: 30px;
}

.garden-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.garden-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.garden-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* AI茶艺体验样式 */
.ai-tea-experience-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.ai-tea-experience-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.ai-tea-experience-section .section-title,
.ai-tea-experience-section .section-subtitle {
  color: white;
}

.ai-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.ai-feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.ai-feature-card:hover::before {
  left: 100%;
}

.ai-feature-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
}

.ai-feature-card h3 {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.ai-feature-card p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 16px;
}

.feature-highlight {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.highlight-tag {
  background: rgba(212, 175, 55, 0.2);
  color: #d4af37;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.ai-demo-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: center;
}

.demo-text h3 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.demo-text p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 20px;
}

.demo-text ul {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.demo-text li {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 8px;
}

.demo-btn {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}

.demo-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-avatar-large {
  text-align: center;
}

.avatar-circle {
  position: relative;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  box-shadow: 0 15px 40px rgba(212, 175, 55, 0.3);
}

.tea-leaf-large {
  font-size: 3rem;
  animation: float 3s ease-in-out infinite;
}

.ai-pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(212, 175, 55, 0.5);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

.ai-name {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.ai-status-text {
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 传统茶艺体验样式 */
.tea-experience-section {
  padding: 100px 0;
  background: white;
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.experience-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid rgba(212, 175, 55, 0.1);
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.experience-card:hover {
  border-color: #d4af37;
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(212, 175, 55, 0.2);
}

.experience-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.experience-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.experience-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 24px;
}

.experience-details {
  margin-bottom: 30px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #4b5563;
  font-size: 0.95rem;
}

.detail-item i {
  color: #d4af37;
  width: 16px;
}

/* 茶文化知识样式 */
.tea-knowledge-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.knowledge-tabs {
  max-width: 900px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 12px 24px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  background: white;
  color: #6b7280;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-btn:hover,
.tab-btn.active {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

.tab-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 分类网格 */
.classification-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.classification-item {
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.classification-item h4 {
  color: #d4af37;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.classification-item p {
  color: #6b7280;
  line-height: 1.5;
}

/* 功效列表 */
.benefits-list {
  display: grid;
  gap: 20px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border-left: 4px solid #d4af37;
}

.benefit-item i {
  color: #d4af37;
  font-size: 1.5rem;
  margin-top: 4px;
}

.benefit-item h4 {
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.benefit-item p {
  color: #6b7280;
  line-height: 1.5;
}

/* 冲泡步骤 */
.brewing-steps {
  display: grid;
  gap: 24px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.step-number {
  width: 40px;
  height: 40px;
  background: #d4af37;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.step-content h4 {
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.step-content p {
  color: #6b7280;
  line-height: 1.5;
}

/* 页脚样式 */
.footer {
  background: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  color: #d4af37;
  margin-bottom: 16px;
}

.footer-section p {
  color: #9ca3af;
  line-height: 1.6;
  margin-bottom: 8px;
}

.footer-link {
  display: block;
  color: #9ca3af;
  text-decoration: none;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #d4af37;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 20px;
  text-align: center;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline-item {
    flex-direction: column !important;
    text-align: center;
  }

  .timeline-marker {
    margin: 0 0 20px 0;
  }

  .history-timeline::before {
    display: none;
  }

  .garden-grid,
  .experience-grid {
    grid-template-columns: 1fr;
  }

  .tab-buttons {
    flex-direction: column;
    align-items: center;
  }

  .tab-btn {
    width: 200px;
  }

  .classification-grid {
    grid-template-columns: 1fr;
  }

  .benefit-item,
  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .tab-content {
    padding: 24px;
  }
}
</style>
