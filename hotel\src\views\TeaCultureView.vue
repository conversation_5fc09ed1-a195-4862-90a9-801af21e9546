<template>
  <div class="page-container fade-in">
    <AppNavbar />

    <!-- 主要内容 -->
    <main class="main-content">
      <!-- 现代化茶文化英雄区域 -->
      <section class="tea-culture-hero">
        <div class="hero-background">
          <div class="hero-carousel">
            <div class="hero-slide active" style="background-image: url('/src/assets/images/实地调研/茶叶产品调研/茶叶产品调研10.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/实地调研/茶叶产品调研/茶叶产品调研15.jpg')">
              <div class="slide-overlay"></div>
            </div>
            <div class="hero-slide" style="background-image: url('/src/assets/images/实地调研/茶叶产品调研/茶叶产品调研8.jpg')">
              <div class="slide-overlay"></div>
            </div>
          </div>
        </div>

        <div class="hero-content">
          <div class="hero-badge slide-in-left">
            <span class="badge-icon">🍃</span>
            <span class="badge-text">千年茶韵</span>
          </div>
          <h1 class="hero-title fade-in-up">
            <span class="title-main gradient-text">普洱茶文化</span>
            <span class="title-sub">品味云南茶乡的深厚文化底蕴</span>
          </h1>
          <p class="hero-subtitle slide-in-right">
            从古茶树到现代茶艺，从茶马古道到世界茶文化<br>
            <span class="subtitle-highlight">在这里，每一片茶叶都诉说着千年的故事</span>
          </p>

          <!-- 茶文化统计 -->
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">1700+</div>
              <div class="stat-label">年历史传承</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">300+</div>
              <div class="stat-label">年古茶树</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">50+</div>
              <div class="stat-label">种茶叶品种</div>
            </div>
          </div>

          <div class="hero-buttons fade-in-up">
            <button @click="scrollToSection('tea-history')" class="btn btn-primary btn-large glowing">
              <span class="btn-text">探索茶史</span>
              <span class="btn-icon">📚</span>
            </button>
            <button @click="scrollToSection('ai-tea-assistant')" class="btn btn-outline-white btn-large">
              <span class="btn-text">AI茶文化助手</span>
              <span class="btn-icon">🤖</span>
            </button>
            <button @click="scrollToSection('tea-experience')" class="btn btn-outline-white btn-large">
              <span class="btn-text">体验茶艺</span>
              <span class="btn-icon">🫖</span>
            </button>
          </div>
        </div>

        <!-- 滚动提示 -->
        <div class="scroll-indicator">
          <div class="scroll-text">探索茶文化</div>
          <div class="scroll-arrow">
            <i class="fas fa-chevron-down"></i>
          </div>
        </div>
      </section>

      <!-- 专业茶文化历史区域 -->
      <section id="tea-history" class="tea-history-section">
        <div class="tea-history-background">
          <div class="history-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <div class="section-badge">
              <span class="badge-dot"></span>
              <span>历史传承</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">普洱茶的千年历程</span>
              <span class="title-main">从古茶树到茶马古道的传奇故事</span>
            </h2>
            <p class="section-subtitle">
              普洱茶有着1700多年的历史，是中国茶文化的重要组成部分。从古代的茶马古道到现代的国际茶叶贸易，
              普洱茶见证了中华文明的发展历程，承载着深厚的文化内涵。
            </p>
          </div>

          <div class="history-timeline">
            <div class="timeline-item" data-aos="fade-right">
              <div class="timeline-marker">
                <i class="fas fa-seedling"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-date">东汉时期（25-220年）</div>
                <h3 class="timeline-title">茶叶起源</h3>
                <p class="timeline-desc">
                  据《华阳国志》记载，早在东汉时期，云南地区就有"茶出银生城界诸山"的记载。
                  银生城即今天的景东县，这里的先民开始种植和饮用茶叶，奠定了普洱茶的历史基础。
                  当时的茶叶主要用于药用和祭祀，被视为神圣的植物。
                </p>
                <div class="timeline-features">
                  <span class="feature-tag">药用价值</span>
                  <span class="feature-tag">祭祀用途</span>
                  <span class="feature-tag">野生茶树</span>
                </div>
              </div>
            </div>

            <div class="timeline-item" data-aos="fade-left">
              <div class="timeline-marker">
                <i class="fas fa-route"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-date">唐宋时期（618-1279年）</div>
                <h3 class="timeline-title">茶马古道兴起</h3>
                <p class="timeline-desc">
                  唐宋时期，茶马古道正式形成，普洱茶开始大规模对外贸易。马帮驮着茶叶翻山越岭，
                  从云南出发，经过四川、西藏，远达印度、尼泊尔等国。这条古道不仅是商贸通道，
                  更是文化交流的桥梁，促进了各民族间的友好往来。
                </p>
                <div class="timeline-features">
                  <span class="feature-tag">茶马互市</span>
                  <span class="feature-tag">文化交流</span>
                  <span class="feature-tag">国际贸易</span>
                </div>
              </div>
            </div>

            <div class="timeline-item" data-aos="fade-right">
              <div class="timeline-marker">
                <i class="fas fa-crown"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-date">明清时期（1368-1912年）</div>
                <h3 class="timeline-title">贡茶地位确立</h3>
                <p class="timeline-desc">
                  明清时期，普洱茶被正式列为贡茶，每年都要向朝廷进贡。清朝雍正年间设立普洱府，
                  专门管理茶叶生产和贸易。这一时期，普洱茶的制作工艺日趋完善，形成了独特的发酵工艺，
                  奠定了现代普洱茶的基础。
                </p>
                <div class="timeline-features">
                  <span class="feature-tag">皇室贡茶</span>
                  <span class="feature-tag">工艺完善</span>
                  <span class="feature-tag">官方管理</span>
                </div>
              </div>
            </div>

            <div class="timeline-item" data-aos="fade-left">
              <div class="timeline-marker">
                <i class="fas fa-industry"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-date">民国时期（1912-1949年）</div>
                <h3 class="timeline-title">现代化发展</h3>
                <p class="timeline-desc">
                  民国时期，普洱茶开始向现代化生产转变。1938年，中国茶叶公司成立，开始对普洱茶进行标准化生产。
                  这一时期，普洱茶的品质得到进一步提升，开始出现了一些知名的茶庄和品牌，
                  为新中国成立后的茶叶发展奠定了基础。
                </p>
                <div class="timeline-features">
                  <span class="feature-tag">标准化生产</span>
                  <span class="feature-tag">品牌建设</span>
                  <span class="feature-tag">质量提升</span>
                </div>
              </div>
            </div>

            <div class="timeline-item" data-aos="fade-right">
              <div class="timeline-marker">
                <i class="fas fa-globe-asia"></i>
              </div>
              <div class="timeline-content">
                <div class="timeline-date">现代时期（1949年至今）</div>
                <h3 class="timeline-title">走向世界</h3>
                <p class="timeline-desc">
                  新中国成立后，普洱茶迎来了新的发展机遇。1973年，昆明茶厂成功研制出人工发酵技术，
                  创造了熟普洱茶。21世纪以来，普洱茶走向世界，成为中国茶文化的重要代表，
                  在国际市场上享有很高的声誉，被誉为"可以喝的古董"。
                </p>
                <div class="timeline-features">
                  <span class="feature-tag">技术创新</span>
                  <span class="feature-tag">国际认可</span>
                  <span class="feature-tag">文化传播</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 茶马古道文化展示 -->
          <div class="tea-road-culture">
            <div class="culture-content">
              <div class="culture-text">
                <h3>茶马古道：连接世界的文化纽带</h3>
                <p>
                  茶马古道是世界上地势最高、山路最险、距离最遥远的贸易通道，被誉为"南方丝绸之路"。
                  这条古道不仅促进了茶叶贸易的发展，更重要的是推动了不同民族、不同文化之间的交流与融合。
                </p>
                <div class="culture-highlights">
                  <div class="highlight-item">
                    <i class="fas fa-mountain"></i>
                    <div>
                      <h4>地理奇迹</h4>
                      <p>跨越横断山脉，连接云南、四川、西藏</p>
                    </div>
                  </div>
                  <div class="highlight-item">
                    <i class="fas fa-handshake"></i>
                    <div>
                      <h4>文化交融</h4>
                      <p>促进汉、藏、彝等多民族文化交流</p>
                    </div>
                  </div>
                  <div class="highlight-item">
                    <i class="fas fa-coins"></i>
                    <div>
                      <h4>经济繁荣</h4>
                      <p>带动沿线地区经济发展和城镇兴起</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="culture-visual">
                <div class="tea-road-map">
                  <div class="map-point start">
                    <span class="point-dot"></span>
                    <span class="point-label">普洱</span>
                  </div>
                  <div class="map-path"></div>
                  <div class="map-point middle">
                    <span class="point-dot"></span>
                    <span class="point-label">大理</span>
                  </div>
                  <div class="map-path"></div>
                  <div class="map-point end">
                    <span class="point-dot"></span>
                    <span class="point-label">拉萨</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 茶园介绍区域 -->
      <section class="tea-garden-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">庄园茶园</h2>
            <p class="section-subtitle">走进我们的生态茶园，感受大自然的馈赠</p>
          </div>

          <div class="garden-grid">
            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研15.jpg" alt="古茶树园">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">千年古茶树园</h3>
                <p class="garden-desc">拥有300多年历史的古茶树群落，每一片茶叶都承载着岁月的沉淀。</p>
                <div class="garden-features">
                  <span class="feature-tag">有机种植</span>
                  <span class="feature-tag">古树茶</span>
                  <span class="feature-tag">生态环保</span>
                </div>
              </div>
            </div>

            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品调研8.jpg" alt="生态茶园">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">生态有机茶园</h3>
                <p class="garden-desc">采用现代生态种植技术，确保每一片茶叶的纯净与健康。</p>
                <div class="garden-features">
                  <span class="feature-tag">无农药</span>
                  <span class="feature-tag">纯天然</span>
                  <span class="feature-tag">可持续</span>
                </div>
              </div>
            </div>

            <div class="garden-card">
              <div class="garden-image">
                <img src="@/assets/images/实地调研/茶叶产品调研/茶叶产品制作工具.jpg" alt="茶叶加工">
                <div class="garden-overlay"></div>
              </div>
              <div class="garden-content">
                <h3 class="garden-title">传统制茶工坊</h3>
                <p class="garden-desc">传承古法制茶工艺，结合现代技术，打造高品质普洱茶。</p>
                <div class="garden-features">
                  <span class="feature-tag">传统工艺</span>
                  <span class="feature-tag">手工制作</span>
                  <span class="feature-tag">品质保证</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- AI茶文化助手区域 -->
      <section id="ai-tea-assistant" class="ai-tea-assistant-section">
        <div class="ai-background">
          <div class="ai-particles"></div>
          <div class="ai-grid-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <div class="section-badge">
              <span class="badge-dot ai-pulse"></span>
              <span>AI智能助手</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">茶茶AI助手</span>
              <span class="title-main">您的专属茶文化顾问</span>
            </h2>
            <p class="section-subtitle">
              融合深度学习技术与千年茶文化智慧，为您提供24小时专业的茶文化咨询服务。
              无论是茶叶识别、冲泡技巧，还是文化典故，茶茶都能为您详细解答。
            </p>
          </div>

          <!-- AI助手主要展示区域 -->
          <div class="ai-main-showcase">
            <div class="ai-avatar-section">
              <div class="ai-avatar-container">
                <div class="ai-avatar-circle">
                  <img src="/src/assets/images/IP形象/茶茶正面.png" alt="茶茶AI助手" class="ai-avatar-image">
                  <div class="ai-status-ring"></div>
                  <div class="ai-pulse-ring"></div>
                </div>
                <div class="ai-info">
                  <h3 class="ai-name">茶茶</h3>
                  <p class="ai-title">AI茶文化专家</p>
                  <div class="ai-status">
                    <span class="status-dot online"></span>
                    <span class="status-text">在线服务中</span>
                  </div>
                </div>
              </div>

              <!-- AI能力展示 -->
              <div class="ai-capabilities">
                <div class="capability-item">
                  <div class="capability-icon">
                    <i class="fas fa-brain"></i>
                  </div>
                  <div class="capability-content">
                    <h4>智能识别</h4>
                    <p>精准识别茶叶品种、年份、产地</p>
                  </div>
                </div>
                <div class="capability-item">
                  <div class="capability-icon">
                    <i class="fas fa-book-open"></i>
                  </div>
                  <div class="capability-content">
                    <h4>文化解读</h4>
                    <p>深度解析茶文化历史与典故</p>
                  </div>
                </div>
                <div class="capability-item">
                  <div class="capability-icon">
                    <i class="fas fa-coffee"></i>
                  </div>
                  <div class="capability-content">
                    <h4>冲泡指导</h4>
                    <p>个性化冲泡建议与技巧分享</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- AI对话演示区域 -->
            <div class="ai-chat-demo">
              <div class="chat-window">
                <div class="chat-header">
                  <div class="chat-title">
                    <i class="fas fa-comments"></i>
                    与茶茶对话
                  </div>
                  <div class="chat-status">
                    <span class="typing-indicator">
                      <span></span>
                      <span></span>
                      <span></span>
                    </span>
                    正在输入...
                  </div>
                </div>
                <div class="chat-messages">
                  <div class="message ai-message">
                    <div class="message-avatar">
                      <img src="/src/assets/images/IP形象/茶茶正面.png" alt="茶茶">
                    </div>
                    <div class="message-content">
                      <p>您好！我是茶茶，您的专属茶文化顾问。我可以为您介绍普洱茶的历史、制作工艺、冲泡技巧等知识。请问您想了解什么呢？</p>
                    </div>
                  </div>
                  <div class="message user-message">
                    <div class="message-content">
                      <p>请介绍一下普洱茶的分类</p>
                    </div>
                  </div>
                  <div class="message ai-message">
                    <div class="message-avatar">
                      <img src="/src/assets/images/IP形象/茶茶正面.png" alt="茶茶">
                    </div>
                    <div class="message-content">
                      <p>普洱茶主要分为生茶和熟茶两大类：</p>
                      <ul>
                        <li><strong>生茶</strong>：未经人工发酵，保持茶叶原有特性，适合长期收藏</li>
                        <li><strong>熟茶</strong>：经过人工发酵，茶性温和，适合日常饮用</li>
                      </ul>
                      <p>您想了解哪种茶的详细信息呢？</p>
                    </div>
                  </div>
                </div>
                <div class="chat-input-area">
                  <div class="input-container">
                    <input type="text" placeholder="输入您的问题..." class="chat-input" v-model="demoMessage">
                    <button class="send-btn" @click="sendDemoMessage">
                      <i class="fas fa-paper-plane"></i>
                    </button>
                  </div>
                  <div class="quick-questions">
                    <button class="quick-btn" @click="askQuickQuestion('如何冲泡普洱茶？')">如何冲泡普洱茶？</button>
                    <button class="quick-btn" @click="askQuickQuestion('普洱茶有什么功效？')">普洱茶有什么功效？</button>
                    <button class="quick-btn" @click="askQuickQuestion('如何保存普洱茶？')">如何保存普洱茶？</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI功能特色展示 -->
          <div class="ai-features-showcase">
            <div class="feature-card" @click="openAIChat('recognition')">
              <div class="feature-icon">
                <i class="fas fa-camera"></i>
              </div>
              <div class="feature-content">
                <h3>智能茶叶识别</h3>
                <p>上传茶叶照片，AI瞬间识别品种、年份、产地等详细信息</p>
                <div class="feature-tags">
                  <span class="tag">图像识别</span>
                  <span class="tag">专业分析</span>
                </div>
              </div>
              <div class="feature-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="feature-card" @click="openAIChat('brewing')">
              <div class="feature-icon">
                <i class="fas fa-coffee"></i>
              </div>
              <div class="feature-content">
                <h3>个性化冲泡指导</h3>
                <p>根据茶叶类型和个人喜好，提供专业的冲泡步骤和技巧</p>
                <div class="feature-tags">
                  <span class="tag">个性定制</span>
                  <span class="tag">专业指导</span>
                </div>
              </div>
              <div class="feature-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="feature-card" @click="openAIChat('culture')">
              <div class="feature-icon">
                <i class="fas fa-scroll"></i>
              </div>
              <div class="feature-content">
                <h3>茶文化知识库</h3>
                <p>深度学习茶文化历史、典故、制作工艺等丰富知识</p>
                <div class="feature-tags">
                  <span class="tag">文化传承</span>
                  <span class="tag">深度解析</span>
                </div>
              </div>
              <div class="feature-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>

            <div class="feature-card" @click="openAIChat('health')">
              <div class="feature-icon">
                <i class="fas fa-heart"></i>
              </div>
              <div class="feature-content">
                <h3>健康养生建议</h3>
                <p>基于个人体质和需求，推荐适合的茶叶和饮用方式</p>
                <div class="feature-tags">
                  <span class="tag">健康养生</span>
                  <span class="tag">个性推荐</span>
                </div>
              </div>
              <div class="feature-arrow">
                <i class="fas fa-arrow-right"></i>
              </div>
            </div>
          </div>

          <!-- 立即体验按钮 -->
          <div class="ai-cta-section">
            <button @click="startAIChat" class="ai-cta-btn">
              <span class="btn-icon">🤖</span>
              <span class="btn-text">立即与茶茶对话</span>
              <span class="btn-subtitle">开启您的茶文化探索之旅</span>
            </button>
          </div>
        </div>
      </section>

      <!-- 传统茶艺体验区域 -->
      <section id="tea-experience" class="tea-experience-section">
        <div class="container">
          <div class="section-header">
            <h2 class="section-title">传统茶艺体验</h2>
            <p class="section-subtitle">在专业茶艺师的指导下，体验正宗的普洱茶文化</p>
          </div>

          <div class="experience-grid">
            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-leaf"></i>
              </div>
              <h3 class="experience-title">茶叶品鉴</h3>
              <p class="experience-desc">学习识别不同年份、不同产区的普洱茶，掌握品茶的基本技巧。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：2小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：2-8人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥188/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>

            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-hands"></i>
              </div>
              <h3 class="experience-title">制茶工艺</h3>
              <p class="experience-desc">亲手参与普洱茶的制作过程，从采摘到压制，体验传统制茶工艺。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：4小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：4-12人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥388/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>

            <div class="experience-card">
              <div class="experience-icon">
                <i class="fas fa-meditation"></i>
              </div>
              <h3 class="experience-title">茶道禅修</h3>
              <p class="experience-desc">在宁静的茶室中，通过茶道修身养性，感受内心的平静与和谐。</p>
              <div class="experience-details">
                <div class="detail-item">
                  <i class="fas fa-clock"></i>
                  <span>体验时长：3小时</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-users"></i>
                  <span>适合人数：1-6人</span>
                </div>
                <div class="detail-item">
                  <i class="fas fa-tag"></i>
                  <span>体验价格：￥288/人</span>
                </div>
              </div>
              <button @click="handleBookingClick" class="btn btn-primary">立即预约</button>
            </div>
          </div>
        </div>
      </section>

      <!-- 专业茶文化知识区域 -->
      <section class="tea-knowledge-section">
        <div class="knowledge-background">
          <div class="knowledge-pattern"></div>
        </div>
        <div class="container">
          <div class="section-header">
            <div class="section-badge">
              <span class="badge-dot"></span>
              <span>专业知识</span>
            </div>
            <h2 class="section-title">
              <span class="title-highlight">茶文化百科</span>
              <span class="title-main">深入了解普洱茶的精髓</span>
            </h2>
            <p class="section-subtitle">
              从茶叶分类到冲泡技巧，从健康功效到文化内涵，
              全方位了解普洱茶的专业知识，成为真正的茶文化爱好者。
            </p>
          </div>

          <div class="knowledge-tabs">
            <div class="tab-navigation">
              <button
                v-for="tab in knowledgeTabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="['tab-btn', { active: activeTab === tab.id }]"
              >
                <i :class="tab.icon"></i>
                <span>{{ tab.title }}</span>
                <div class="tab-indicator"></div>
              </button>
            </div>

            <div class="tab-content">
              <!-- 茶叶分类标签页 -->
              <div v-if="activeTab === 'classification'" class="tab-panel">
                <div class="knowledge-content">
                  <div class="content-header">
                    <h3>普洱茶分类详解</h3>
                    <p>普洱茶根据制作工艺和发酵程度，主要分为生茶和熟茶两大类，每类都有其独特的特点和价值。</p>
                  </div>

                  <div class="classification-showcase">
                    <div class="tea-type-card">
                      <div class="tea-type-header">
                        <div class="tea-type-icon">
                          <img src="/src/assets/images/产品/茶饼/茶饼原料1.jpg" alt="生茶">
                        </div>
                        <div class="tea-type-info">
                          <h4>生茶（生普）</h4>
                          <p class="tea-type-subtitle">自然发酵，原味醇香</p>
                        </div>
                      </div>
                      <div class="tea-type-content">
                        <div class="tea-description">
                          <p>生茶是指新鲜的茶叶采摘后，经过杀青、揉捻、晒干等工艺制成，未经人工发酵的普洱茶。保持了茶叶的原始特性，具有很强的收藏价值。</p>
                        </div>
                        <div class="tea-characteristics">
                          <div class="characteristic-item">
                            <i class="fas fa-eye"></i>
                            <div>
                              <strong>外观特征</strong>
                              <p>条索紧结，色泽墨绿</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-coffee"></i>
                            <div>
                              <strong>汤色口感</strong>
                              <p>汤色金黄清澈，口感清香回甘</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-clock"></i>
                            <div>
                              <strong>陈化特点</strong>
                              <p>随时间增长，口感越发醇厚</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-users"></i>
                            <div>
                              <strong>适宜人群</strong>
                              <p>喜欢清淡口感，有收藏兴趣的茶友</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="tea-type-card">
                      <div class="tea-type-header">
                        <div class="tea-type-icon">
                          <img src="/src/assets/images/产品/普洱茶/普洱茶1.jpg" alt="熟茶">
                        </div>
                        <div class="tea-type-info">
                          <h4>熟茶（熟普）</h4>
                          <p class="tea-type-subtitle">人工发酵，温和醇厚</p>
                        </div>
                      </div>
                      <div class="tea-type-content">
                        <div class="tea-description">
                          <p>熟茶是经过人工发酵（渥堆发酵）工艺制成的普洱茶。这种工艺模拟了生茶自然陈化的过程，使茶叶在短时间内达到温和的口感。</p>
                        </div>
                        <div class="tea-characteristics">
                          <div class="characteristic-item">
                            <i class="fas fa-eye"></i>
                            <div>
                              <strong>外观特征</strong>
                              <p>条索肥壮，色泽红褐</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-coffee"></i>
                            <div>
                              <strong>汤色口感</strong>
                              <p>汤色红浓明亮，口感醇厚甘甜</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-heart"></i>
                            <div>
                              <strong>茶性特点</strong>
                              <p>茶性温和，不刺激肠胃</p>
                            </div>
                          </div>
                          <div class="characteristic-item">
                            <i class="fas fa-users"></i>
                            <div>
                              <strong>适宜人群</strong>
                              <p>肠胃敏感，喜欢醇厚口感的茶友</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="activeTab === 'benefits'" class="tab-panel">
                <div class="benefits-list">
                  <div class="benefit-item">
                    <i class="fas fa-heart"></i>
                    <div>
                      <h4>降脂减肥</h4>
                      <p>普洱茶含有丰富的茶多酚，有助于降低血脂，促进新陈代谢。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-shield-alt"></i>
                    <div>
                      <h4>抗氧化</h4>
                      <p>茶叶中的抗氧化成分有助于延缓衰老，增强免疫力。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-brain"></i>
                    <div>
                      <h4>提神醒脑</h4>
                      <p>适量的咖啡因有助于提高注意力，缓解疲劳。</p>
                    </div>
                  </div>
                  <div class="benefit-item">
                    <i class="fas fa-stomach"></i>
                    <div>
                      <h4>养胃护胃</h4>
                      <p>熟普洱茶性温和，有助于保护胃黏膜，促进消化。</p>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="activeTab === 'brewing'" class="tab-panel">
                <div class="brewing-steps">
                  <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                      <h4>温杯洁具</h4>
                      <p>用热水冲洗茶具，提高茶具温度，去除异味。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                      <h4>投茶醒茶</h4>
                      <p>按1:20的比例投茶，用热水快速冲洗茶叶，倒掉第一泡。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                      <h4>冲泡品饮</h4>
                      <p>用95-100°C的热水冲泡，第一泡30秒，后续每泡延长10秒。</p>
                    </div>
                  </div>
                  <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                      <h4>品味回甘</h4>
                      <p>细品茶汤的香气、滋味和回甘，感受普洱茶的独特魅力。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>普洱蘑菇庄园</h3>
            <p>隐于森林深处，与自然共生的生态民宿体验</p>
          </div>
          <div class="footer-section">
            <h4>联系我们</h4>
            <p>地址：云南省普洱市思茅区</p>
            <p>电话：400-123-4567</p>
            <p>邮箱：<EMAIL></p>
          </div>
          <div class="footer-section">
            <h4>快速链接</h4>
            <router-link to="/" class="footer-link">首页</router-link>
            <router-link to="/tea-culture" class="footer-link">茶文化</router-link>
            <router-link to="/products" class="footer-link">民宿产品</router-link>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 普洱蘑菇庄园民宿. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 滚动到顶部按钮 -->
    <button
      v-show="showScrollTop"
      @click="scrollToTop"
      class="scroll-top-btn floating"
      title="回到顶部"
    >
      <i class="fas fa-chevron-up"></i>
    </button>

    <!-- 全局IP助手 -->
    <!-- <GlobalIPAssistant /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useRouter } from 'vue-router'
import AppNavbar from '../components/AppNavbar.vue'

const auth = useAuthStore()
const router = useRouter()

// 响应式数据
const activeTab = ref('classification')
const showScrollTop = ref(false)
const demoMessage = ref('')
const aiChatMessages = ref([
  {
    type: 'ai',
    content: '您好！我是茶茶，您的专属茶文化顾问。我可以为您介绍普洱茶的历史、制作工艺、冲泡技巧等知识。请问您想了解什么呢？',
    timestamp: new Date()
  }
])

// 知识标签页数据
const knowledgeTabs = [
  {
    id: 'classification',
    title: '茶叶分类',
    icon: 'fas fa-leaf'
  },
  {
    id: 'benefits',
    title: '功效作用',
    icon: 'fas fa-heart'
  },
  {
    id: 'brewing',
    title: '冲泡技巧',
    icon: 'fas fa-coffee'
  },
  {
    id: 'culture',
    title: '文化内涵',
    icon: 'fas fa-book'
  }
]

// 滚动到指定区域
const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 滚动到顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 监听滚动事件
const handleScroll = () => {
  showScrollTop.value = window.scrollY > 500
}

// AI助手相关功能
const startAIChat = () => {
  scrollToSection('ai-tea-assistant')
}

const openAIChat = (type: string) => {
  let message = ''
  switch (type) {
    case 'recognition':
      message = '我想了解茶叶识别功能'
      break
    case 'brewing':
      message = '请教我如何冲泡普洱茶'
      break
    case 'culture':
      message = '我想了解普洱茶的文化历史'
      break
    case 'health':
      message = '普洱茶有什么健康功效？'
      break
    default:
      message = '你好，我想了解茶文化'
  }

  // 添加用户消息
  aiChatMessages.value.push({
    type: 'user',
    content: message,
    timestamp: new Date()
  })

  // 模拟AI回复
  setTimeout(() => {
    let aiResponse = ''
    switch (type) {
      case 'recognition':
        aiResponse = '我可以帮您识别茶叶品种！请上传茶叶照片，我会分析其品种、年份、产地等信息。我的识别准确率达到95%以上，基于深度学习技术训练。'
        break
      case 'brewing':
        aiResponse = '冲泡普洱茶的关键在于水温、投茶量和冲泡时间：\n\n1. 水温：生茶95-100℃，熟茶100℃\n2. 投茶量：150ml水配7-8g茶叶\n3. 冲泡时间：第一泡30秒，后续每泡增加10-15秒\n4. 洗茶：第一泡用于洗茶，不饮用\n\n您想了解具体哪种茶的冲泡方法呢？'
        break
      case 'culture':
        aiResponse = '普洱茶有着1700多年的历史，起源于云南。从茶马古道到现代，它见证了中华文明的发展。普洱茶不仅是饮品，更是文化的载体，承载着民族智慧和传统工艺。您想了解哪个历史时期的故事呢？'
        break
      case 'health':
        aiResponse = '普洱茶具有多种健康功效：\n\n🌿 降脂减肥：帮助分解脂肪\n❤️ 保护心血管：降低胆固醇\n🛡️ 抗氧化：延缓衰老\n🦠 抗菌消炎：增强免疫力\n🍃 助消化：促进肠胃健康\n\n但请注意适量饮用，孕妇和儿童需谨慎。'
        break
      default:
        aiResponse = '欢迎来到茶文化的世界！我可以为您介绍茶叶分类、冲泡技巧、健康功效、文化历史等各方面知识。请告诉我您最感兴趣的话题。'
    }

    aiChatMessages.value.push({
      type: 'ai',
      content: aiResponse,
      timestamp: new Date()
    })
  }, 1000)

  scrollToSection('ai-tea-assistant')
}

const sendDemoMessage = () => {
  if (!demoMessage.value.trim()) return

  // 添加用户消息
  aiChatMessages.value.push({
    type: 'user',
    content: demoMessage.value,
    timestamp: new Date()
  })

  // 清空输入框
  const userMessage = demoMessage.value
  demoMessage.value = ''

  // 模拟AI回复
  setTimeout(() => {
    let response = '感谢您的提问！'

    if (userMessage.includes('分类') || userMessage.includes('种类')) {
      response = '普洱茶主要分为生茶和熟茶。生茶未经发酵，口感清香；熟茶经过发酵，口感醇厚。还有按产地分的古树茶、台地茶等。您想了解哪种茶的详细信息？'
    } else if (userMessage.includes('冲泡') || userMessage.includes('泡茶')) {
      response = '冲泡普洱茶需要注意水温、投茶量和时间。建议使用100℃开水，茶水比例1:20，第一泡洗茶不饮用。您是想泡生茶还是熟茶呢？'
    } else if (userMessage.includes('功效') || userMessage.includes('作用')) {
      response = '普洱茶具有降脂减肥、保护心血管、抗氧化、助消化等功效。长期适量饮用对身体很有益处。您有特别关心的健康方面吗？'
    } else {
      response = '这是一个很好的问题！普洱茶文化博大精深，我很乐意为您详细解答。您可以问我关于茶叶分类、冲泡技巧、健康功效、历史文化等任何问题。'
    }

    aiChatMessages.value.push({
      type: 'ai',
      content: response,
      timestamp: new Date()
    })
  }, 1500)
}

const askQuickQuestion = (question: string) => {
  demoMessage.value = question
  sendDemoMessage()
}

// 页面滚动动画
const observeElements = () => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in')
      }
    })
  }, { threshold: 0.1 })

  // 观察所有需要动画的元素
  const animateElements = document.querySelectorAll('.timeline-item, .tea-type-card, .feature-card, .ai-feature-card')
  animateElements.forEach(el => observer.observe(el))
}

// 英雄区域轮播
const startHeroCarousel = () => {
  const slides = document.querySelectorAll('.hero-slide')
  if (slides.length <= 1) return

  let currentSlide = 0
  setInterval(() => {
    slides[currentSlide].classList.remove('active')
    currentSlide = (currentSlide + 1) % slides.length
    slides[currentSlide].classList.add('active')
  }, 5000)
}

// 添加页面加载动画
const addPageLoadAnimation = () => {
  const elements = document.querySelectorAll('.fade-in-up, .slide-in-left, .slide-in-right')
  elements.forEach((el, index) => {
    setTimeout(() => {
      el.classList.add('animate-in')
    }, index * 100)
  })
}

// 处理预约按钮点击
const handleBookingClick = () => {
  if (!auth.isAuthenticated) {
    router.push('/login')
    return
  }
  router.push('/ai-rooms')
}

// 页面初始化
onMounted(() => {
  console.log('茶文化页面加载完成')

  // 启动各种动画效果
  setTimeout(() => {
    observeElements()
    startHeroCarousel()
    addPageLoadAnimation()
  }, 100)

  // 添加滚动监听
  window.addEventListener('scroll', handleScroll)
})

// 清理事件监听
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* 现代化茶文化页面样式 */
.page-container {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* 全局动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.6);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.floating {
  animation: float 3s ease-in-out infinite;
}

.glowing {
  animation: glow 2s ease-in-out infinite;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 滚动触发动画 */
.timeline-item,
.tea-type-card,
.feature-card,
.ai-feature-card {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.timeline-item.animate-in,
.tea-type-card.animate-in,
.feature-card.animate-in,
.ai-feature-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* 导航栏样式 */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(212, 175, 55, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #d4af37;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-menu {
  display: flex;
  gap: 32px;
}

.nav-link {
  color: #4b5563;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  color: #d4af37;
  background: rgba(212, 175, 55, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 12px;
}

.user-info {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: #d4af37;
  color: white;
}

.btn-primary:hover {
  background: #b8941f;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #d4af37;
  border: 2px solid #d4af37;
}

.btn-outline:hover {
  background: #d4af37;
  color: white;
}

.btn-large {
  padding: 14px 28px;
  font-size: 1.1rem;
}

/* 主要内容区域 */
.main-content {
  padding-top: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 现代化茶文化英雄区域 */
.tea-culture-hero {
  position: relative;
  height: 100vh;
  min-height: 700px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-carousel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 2s ease-in-out;
}

.hero-slide.active {
  opacity: 1;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.6) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 10;
  text-align: center;
  color: white;
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 0.75rem 1.5rem;
  margin-bottom: 2rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.badge-icon {
  font-size: 1.2rem;
}

.badge-text {
  color: white;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
}

.title-main {
  display: block;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.title-sub {
  display: block;
  font-size: 0.6em;
  color: #d4af37;
  margin-top: 0.5rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: clamp(1.1rem, 2.5vw, 1.4rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2.5rem;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.subtitle-highlight {
  color: #d4af37;
  font-weight: 600;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  margin: 2.5rem 0;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: #d4af37;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.hero-buttons {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 3rem;
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: white;
  z-index: 15;
  animation: pulse 2s ease-in-out infinite;
}

.scroll-text {
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.scroll-arrow {
  font-size: 1.2rem;
  animation: float 1s ease-in-out infinite;
}

/* 区域通用样式 */
.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

/* 茶历史时间线样式 */
.tea-history-section {
  padding: 100px 0;
  background: white;
}

.history-timeline {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.history-timeline::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #d4af37, #b8941f);
  transform: translateX(-50%);
}

.timeline-item {
  display: flex;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
}

.timeline-item:nth-child(odd) {
  flex-direction: row;
}

.timeline-item:nth-child(even) {
  flex-direction: row-reverse;
}

.timeline-marker {
  width: 60px;
  height: 60px;
  background: #d4af37;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  position: relative;
  z-index: 2;
  margin: 0 40px;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.timeline-content {
  flex: 1;
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.timeline-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #d4af37;
  margin-bottom: 12px;
}

.timeline-desc {
  color: #6b7280;
  line-height: 1.6;
}

/* 茶园介绍样式 */
.tea-garden-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.garden-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.garden-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.garden-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.garden-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.garden-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.garden-card:hover .garden-image img {
  transform: scale(1.05);
}

.garden-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.3) 100%);
}

.garden-content {
  padding: 30px;
}

.garden-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.garden-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.garden-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

/* AI茶艺体验样式 */
.ai-tea-experience-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.ai-tea-experience-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.ai-tea-experience-section .section-title,
.ai-tea-experience-section .section-subtitle {
  color: white;
}

.ai-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.ai-feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.ai-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.ai-feature-card:hover::before {
  left: 100%;
}

.ai-feature-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
  box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
}

.ai-feature-card h3 {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.ai-feature-card p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 16px;
}

.feature-highlight {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.highlight-tag {
  background: rgba(212, 175, 55, 0.2);
  color: #d4af37;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.ai-demo-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.demo-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 40px;
  align-items: center;
}

.demo-text h3 {
  color: white;
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.demo-text p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 20px;
}

.demo-text ul {
  list-style: none;
  padding: 0;
  margin-bottom: 30px;
}

.demo-text li {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 8px;
  padding-left: 8px;
}

.demo-btn {
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}

.demo-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ai-avatar-large {
  text-align: center;
}

.avatar-circle {
  position: relative;
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  box-shadow: 0 15px 40px rgba(212, 175, 55, 0.3);
}

.tea-leaf-large {
  font-size: 3rem;
  animation: float 3s ease-in-out infinite;
}

.ai-pulse {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(212, 175, 55, 0.5);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

.ai-name {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.ai-status-text {
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 500;
}

/* 传统茶艺体验样式 */
.tea-experience-section {
  padding: 100px 0;
  background: white;
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
}

.experience-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid rgba(212, 175, 55, 0.1);
  border-radius: 16px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
}

.experience-card:hover {
  border-color: #d4af37;
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(212, 175, 55, 0.2);
}

.experience-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  color: white;
  font-size: 2rem;
}

.experience-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.experience-desc {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 24px;
}

.experience-details {
  margin-bottom: 30px;
}

.detail-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #4b5563;
  font-size: 0.95rem;
}

.detail-item i {
  color: #d4af37;
  width: 16px;
}

/* 茶文化知识样式 */
.tea-knowledge-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.knowledge-tabs {
  max-width: 900px;
  margin: 0 auto;
}

.tab-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 12px 24px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  background: white;
  color: #6b7280;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tab-btn:hover,
.tab-btn.active {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

.tab-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.tab-panel {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 分类网格 */
.classification-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.classification-item {
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.classification-item h4 {
  color: #d4af37;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.classification-item p {
  color: #6b7280;
  line-height: 1.5;
}

/* 功效列表 */
.benefits-list {
  display: grid;
  gap: 20px;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border-left: 4px solid #d4af37;
}

.benefit-item i {
  color: #d4af37;
  font-size: 1.5rem;
  margin-top: 4px;
}

.benefit-item h4 {
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.benefit-item p {
  color: #6b7280;
  line-height: 1.5;
}

/* 冲泡步骤 */
.brewing-steps {
  display: grid;
  gap: 24px;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid rgba(212, 175, 55, 0.2);
}

.step-number {
  width: 40px;
  height: 40px;
  background: #d4af37;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.step-content h4 {
  color: #1f2937;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.step-content p {
  color: #6b7280;
  line-height: 1.5;
}

/* 页脚样式 */
.footer {
  background: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3,
.footer-section h4 {
  color: #d4af37;
  margin-bottom: 16px;
}

.footer-section p {
  color: #9ca3af;
  line-height: 1.6;
  margin-bottom: 8px;
}

.footer-link {
  display: block;
  color: #9ca3af;
  text-decoration: none;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #d4af37;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 20px;
  text-align: center;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline-item {
    flex-direction: column !important;
    text-align: center;
  }

  .timeline-marker {
    margin: 0 0 20px 0;
  }

  .history-timeline::before {
    display: none;
  }

  .garden-grid,
  .experience-grid {
    grid-template-columns: 1fr;
  }

  .tab-buttons {
    flex-direction: column;
    align-items: center;
  }

  .tab-btn {
    width: 200px;
  }

  .classification-grid {
    grid-template-columns: 1fr;
  }

  .benefit-item,
  .step-item {
    flex-direction: column;
    text-align: center;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 1.75rem;
  }

  .tab-content {
    padding: 24px;
  }
}

/* 滚动到顶部按钮 */
.scroll-top-btn {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #d4af37 0%, #b8941f 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
  transition: all 0.3s ease;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.scroll-top-btn:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}
</style>
