<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能AI测试 - 普洱蘑菇庄园民宿</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }
        .test-card h3 {
            margin-top: 0;
            color: #333;
        }
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.secondary {
            background-color: #6c757d;
        }
        button.secondary:hover {
            background-color: #545b62;
        }
        .response {
            margin-top: 15px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 8px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.5;
        }
        .response.success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .response.error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .response.warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .stats {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        .stats h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .custom-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .feature-highlight {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能AI测试中心</h1>
            <p>测试普洱蘑菇庄园民宿的智能AI系统</p>
        </div>

        <div class="feature-highlight">
            <h3>🎯 新功能亮点</h3>
            <p>✨ 智能意图分析 | 🏠 个性化房间推荐 | 📅 自动订单创建 | 🧠 上下文理解</p>
        </div>

        <div class="test-grid">
            <!-- 房间推荐测试 -->
            <div class="test-card">
                <h3>🏠 智能房间推荐</h3>
                <div class="test-buttons">
                    <button onclick="testAI('推荐房间')">基础推荐</button>
                    <button onclick="testAI('推荐便宜的房间')">价格导向</button>
                    <button onclick="testAI('有没有2楼的大床房')">具体需求</button>
                    <button onclick="testAI('100元以下的房间')">预算限制</button>
                </div>
                <div id="roomResult" class="response" style="display: none;"></div>
            </div>

            <!-- 预订流程测试 -->
            <div class="test-card">
                <h3>📅 智能预订流程</h3>
                <div class="test-buttons">
                    <button onclick="testAI('预订201房间')">选择房间</button>
                    <button onclick="testAI('入住2025-07-15，退房2025-07-17，2人入住')">完整预订</button>
                    <button onclick="testAI('明天入住，后天退房，1人')">相对日期</button>
                    <button onclick="testAI('7月20日到7月22日，预订102房间')">日期格式</button>
                </div>
                <div id="bookingResult" class="response" style="display: none;"></div>
            </div>

            <!-- 楼层查询测试 -->
            <div class="test-card">
                <h3>🏢 楼层智能查询</h3>
                <div class="test-buttons">
                    <button onclick="testAI('有没有2楼的房间')">2楼查询</button>
                    <button onclick="testAI('1楼有什么房间')">1楼查询</button>
                    <button onclick="testAI('3楼的房间怎么样')">3楼查询</button>
                    <button onclick="testAI('哪一层比较安静')">楼层建议</button>
                </div>
                <div id="floorResult" class="response" style="display: none;"></div>
            </div>

            <!-- 价格咨询测试 -->
            <div class="test-card">
                <h3>💰 价格智能分析</h3>
                <div class="test-buttons">
                    <button onclick="testAI('房间价格')">价格总览</button>
                    <button onclick="testAI('最便宜的房间')">经济选择</button>
                    <button onclick="testAI('豪华房间多少钱')">高端选择</button>
                    <button onclick="testAI('500元以内的房间')">预算范围</button>
                </div>
                <div id="priceResult" class="response" style="display: none;"></div>
            </div>

            <!-- 对话测试 -->
            <div class="test-card">
                <h3>💬 智能对话测试</h3>
                <div class="test-buttons">
                    <button onclick="testAI('你好')">问候</button>
                    <button onclick="testAI('民宿有什么特色')">咨询</button>
                    <button onclick="testAI('周边有什么好玩的')">周边询问</button>
                    <button onclick="testAI('谢谢')">礼貌用语</button>
                </div>
                <div id="chatResult" class="response" style="display: none;"></div>
            </div>

            <!-- 自定义测试 -->
            <div class="test-card">
                <h3>🎯 自定义测试</h3>
                <input type="text" id="customInput" class="custom-input" placeholder="输入您想测试的问题...">
                <div class="test-buttons">
                    <button onclick="testCustom()">发送测试</button>
                    <button class="secondary" onclick="clearResults()">清空结果</button>
                </div>
                <div id="customResult" class="response" style="display: none;"></div>
            </div>
        </div>

        <div class="stats">
            <h3>📊 测试统计</h3>
            <div id="statsContent">
                <p>总测试次数: <span id="totalTests">0</span></p>
                <p>成功响应: <span id="successCount">0</span></p>
                <p>平均响应时间: <span id="avgTime">0</span>ms</p>
                <p>最后测试: <span id="lastTest">未开始</span></p>
            </div>
        </div>
    </div>

    <script>
        let testStats = {
            total: 0,
            success: 0,
            totalTime: 0,
            lastTest: ''
        };

        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${type}`;
            element.textContent = content;
        }

        async function testAI(message) {
            const startTime = Date.now();
            testStats.total++;
            testStats.lastTest = message;

            try {
                // 确定结果显示区域
                let resultId = 'customResult';
                if (message.includes('推荐') || message.includes('房间')) resultId = 'roomResult';
                else if (message.includes('预订') || message.includes('入住')) resultId = 'bookingResult';
                else if (message.includes('楼') || message.includes('层')) resultId = 'floorResult';
                else if (message.includes('价格') || message.includes('钱') || message.includes('便宜') || message.includes('贵')) resultId = 'priceResult';
                else if (message.includes('你好') || message.includes('特色') || message.includes('谢谢')) resultId = 'chatResult';

                showResult(resultId, `🤖 正在处理: "${message}"...`, 'warning');

                // 调用AI选房页面的API
                const response = await fetch('http://localhost:8080/chat/messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: message,
                        user: 'test-user',
                        inputs: {}
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;
                testStats.totalTime += responseTime;

                if (response.ok) {
                    // 处理SSE流式响应
                    const reader = response.body?.getReader();
                    let fullResponse = '';

                    if (reader) {
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            const chunk = new TextDecoder().decode(value);
                            const lines = chunk.split('\n');

                            for (const line of lines) {
                                if (line.startsWith('data: ')) {
                                    const data = line.slice(6).trim();
                                    if (data && data !== '[DONE]') {
                                        try {
                                            const jsonData = JSON.parse(data);
                                            if (jsonData.event === 'message' && jsonData.answer) {
                                                fullResponse += jsonData.answer;
                                            }
                                        } catch (e) {
                                            // 如果不是JSON格式，直接添加文本
                                            fullResponse += data;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (fullResponse.trim()) {
                        testStats.success++;
                        showResult(resultId, `✅ AI响应 (${responseTime}ms):\n\n${fullResponse}`, 'success');
                    } else {
                        showResult(resultId, `⚠️ 收到空响应 (${responseTime}ms)`, 'warning');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                showResult(resultId || 'customResult', `❌ 测试失败 (${responseTime}ms): ${error.message}`, 'error');
            }

            updateStats();
        }

        function testCustom() {
            const input = document.getElementById('customInput');
            const message = input.value.trim();
            if (message) {
                testAI(message);
                input.value = '';
            }
        }

        function clearResults() {
            const results = document.querySelectorAll('.response');
            results.forEach(result => {
                result.style.display = 'none';
                result.textContent = '';
            });
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('successCount').textContent = testStats.success;
            document.getElementById('avgTime').textContent = testStats.total > 0 ? 
                Math.round(testStats.totalTime / testStats.total) : 0;
            document.getElementById('lastTest').textContent = testStats.lastTest || '未开始';
        }

        // 支持回车键发送
        document.getElementById('customInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testCustom();
            }
        });

        // 页面加载时显示欢迎信息
        window.addEventListener('load', () => {
            showResult('chatResult', '🍄 欢迎使用智能AI测试中心！\n\n请选择上方的测试按钮，或在自定义测试区域输入您的问题。\n\n新的智能AI系统具备以下特点：\n• 🧠 智能意图分析\n• 🏠 个性化房间推荐\n• 📅 自动订单创建\n• 💰 价格智能分析\n• 🏢 楼层偏好理解\n\n开始测试吧！', 'success');
        });
    </script>
</body>
</html>
