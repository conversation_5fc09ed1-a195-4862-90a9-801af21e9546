<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI硬编码修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI硬编码修复测试</h1>
        
        <div class="test-section">
            <h3>📡 测试后端AI响应</h3>
            <button onclick="testBackendAI('推荐房间')">测试"推荐房间"</button>
            <button onclick="testBackendAI('预订201房间')">测试"预订201房间"</button>
            <button onclick="testBackendAI('有没有2楼的房间')">测试"有没有2楼的房间"</button>
            <button onclick="testBackendAI('普洱茶文化')">测试"普洱茶文化"</button>
            <div id="backendResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🤖 测试前端AI助手</h3>
            <button onclick="testFrontendAI('你好')">测试"你好"</button>
            <button onclick="testFrontendAI('推荐房间')">测试"推荐房间"</button>
            <button onclick="testFrontendAI('谢谢')">测试"谢谢"</button>
            <button onclick="testFrontendAI('再见')">测试"再见"</button>
            <div id="frontendResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🏠 测试AI选房页面</h3>
            <button onclick="testRoomSelection('推荐房间')">测试"推荐房间"</button>
            <button onclick="testRoomSelection('预订201')">测试"预订201"</button>
            <button onclick="testRoomSelection('有没有便宜的房间')">测试"有没有便宜的房间"</button>
            <div id="roomResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果分析</h3>
            <button onclick="analyzeResults()">分析所有测试结果</button>
            <div id="analysisResult" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        let testResults = [];

        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${type}`;
            element.textContent = content;
        }

        async function testBackendAI(message) {
            try {
                showResult('backendResult', `正在测试后端AI: ${message}...`, 'warning');
                
                const response = await fetch('http://localhost:8080/ai-chat/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        userId: 'test-user'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    const result = data.data || data.message || '无响应内容';
                    
                    // 检查是否是硬编码回复
                    const isHardcoded = checkIfHardcoded(result);
                    const resultType = isHardcoded ? 'warning' : 'success';
                    const prefix = isHardcoded ? '⚠️ 可能是硬编码回复:\n' : '✅ 智能回复:\n';
                    
                    showResult('backendResult', prefix + result, resultType);
                    
                    testResults.push({
                        type: 'backend',
                        message: message,
                        response: result,
                        isHardcoded: isHardcoded
                    });
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                showResult('backendResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testFrontendAI(message) {
            try {
                showResult('frontendResult', `正在测试前端AI: ${message}...`, 'warning');
                
                // 模拟前端AI处理逻辑
                const result = simulateFrontendAI(message);
                
                // 检查是否是硬编码回复
                const isHardcoded = checkIfHardcoded(result);
                const resultType = isHardcoded ? 'warning' : 'success';
                const prefix = isHardcoded ? '⚠️ 可能是硬编码回复:\n' : '✅ 智能回复:\n';
                
                showResult('frontendResult', prefix + result, resultType);
                
                testResults.push({
                    type: 'frontend',
                    message: message,
                    response: result,
                    isHardcoded: isHardcoded
                });
            } catch (error) {
                showResult('frontendResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        async function testRoomSelection(message) {
            try {
                showResult('roomResult', `正在测试AI选房: ${message}...`, 'warning');
                
                // 模拟AI选房页面的处理逻辑
                const result = simulateRoomSelection(message);
                
                // 检查是否是硬编码回复
                const isHardcoded = checkIfHardcoded(result);
                const resultType = isHardcoded ? 'warning' : 'success';
                const prefix = isHardcoded ? '⚠️ 可能是硬编码回复:\n' : '✅ 智能回复:\n';
                
                showResult('roomResult', prefix + result, resultType);
                
                testResults.push({
                    type: 'room',
                    message: message,
                    response: result,
                    isHardcoded: isHardcoded
                });
            } catch (error) {
                showResult('roomResult', `❌ 测试失败: ${error.message}`, 'error');
            }
        }

        function checkIfHardcoded(response) {
            // 检查是否包含硬编码的特征
            const hardcodedPatterns = [
                /普洱茶是我们这里的特色！我们民宿周围有很多茶园/,
                /我们的蘑菇庄园种植了多种优质蘑菇/,
                /普洱有很多美丽的景点！推荐您游览/,
                /我们有普洱茶香木屋（288元\/晚）/,
                /我可以帮您快速导航！您可以直接告诉我/,
                /感谢您对我们酒店的信任，欢迎入住我们酒店/,
                /房价350元\/晚/,
                /我可以为您定酒店，为您推荐美食/
            ];
            
            return hardcodedPatterns.some(pattern => pattern.test(response));
        }

        function simulateFrontendAI(message) {
            // 模拟前端AI的降级回复
            if (message.includes('你好') || message.includes('您好')) {
                return '🍄 您好！我是普洱蘑菇庄园民宿的AI助手。AI服务暂时不可用，您可以稍后重试或使用导航功能。';
            }
            if (message.includes('谢谢') || message.includes('感谢')) {
                return '🍄 不客气！AI服务暂时不可用，您可以稍后重试。';
            }
            if (message.includes('再见') || message.includes('拜拜')) {
                return '🍄 再见！期待您的到来！';
            }
            return '🍄 AI服务暂时不可用，您可以稍后重试或使用导航功能。';
        }

        function simulateRoomSelection(message) {
            if (message.includes('推荐') && message.includes('房间')) {
                return '🍄 根据您的需求，为您推荐以下优质房间：\n\n1.房间号201 🌧️雨林景观豪华蘑菇屋\n价格：19元/晚\n类型：大床房 | 面积：25㎡\n\n需要帮您按价格排序或了解其他细节吗？✨';
            }
            if (message.includes('预订201') || message.includes('201')) {
                return '🏠 您选择了201号房 - 雨林景观豪华蘑菇屋\n💰 价格：19元/晚\n\n请告诉我您的入住和退房日期：\n📅 格式示例：\n• "入住2025-07-15，退房2025-07-17"\n• "明天入住，后天退房"\n• "7月15日到7月17日"';
            }
            return '🍄 您好！我是AI选房助手。您可以说：\n• "推荐房间" - 获取房间推荐\n• "预订201" - 预订指定房间\n• 或者直接告诉我您的需求';
        }

        function analyzeResults() {
            if (testResults.length === 0) {
                showResult('analysisResult', '❌ 没有测试结果可分析，请先运行一些测试。', 'error');
                return;
            }

            const totalTests = testResults.length;
            const hardcodedCount = testResults.filter(r => r.isHardcoded).length;
            const intelligentCount = totalTests - hardcodedCount;

            let analysis = `📊 测试结果分析:\n\n`;
            analysis += `总测试数: ${totalTests}\n`;
            analysis += `智能回复: ${intelligentCount} (${Math.round(intelligentCount/totalTests*100)}%)\n`;
            analysis += `硬编码回复: ${hardcodedCount} (${Math.round(hardcodedCount/totalTests*100)}%)\n\n`;

            if (hardcodedCount > 0) {
                analysis += `⚠️ 发现硬编码回复的测试:\n`;
                testResults.filter(r => r.isHardcoded).forEach((result, index) => {
                    analysis += `${index + 1}. [${result.type}] "${result.message}"\n`;
                });
                analysis += `\n建议: 这些回复可能需要进一步优化，确保使用Dify AI服务。`;
            } else {
                analysis += `✅ 所有测试都显示智能回复，硬编码问题已修复！`;
            }

            const resultType = hardcodedCount === 0 ? 'success' : 'warning';
            showResult('analysisResult', analysis, resultType);
        }
    </script>
</body>
</html>
