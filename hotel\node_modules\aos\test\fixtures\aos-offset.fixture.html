<!DOCTYPE html>
<html>
  <head>
    <title></title>
    <style type="text/css">
      * {box-sizing: border-box;}
      body {
        margin: 0; padding: 0 20px; text-align: center;
      }
      .aos-item {
        width: 100%; height: 130px; margin: 0 0 20px 0; background: #ccc;
      }
    </style>
  </head>
  <body class="body">
    <div class="aos-item aos-item--1" data-aos-offset="50" data-aos="fade"></div>
    <div class="aos-item aos-item--2" data-aos-offset="50" data-aos="fade-up"></div>
    <div class="aos-item aos-item--3" data-aos-offset="50" data-aos="fade-down"></div>
    <div class="aos-item aos-item--4" data-aos-offset="50" data-aos="fade-left"></div>
    <div class="aos-item aos-item--5" data-aos-offset="50" data-aos="fade-right"></div>
    <div class="aos-item aos-item--6" data-aos-offset="50" data-aos="flip-up"></div>
    <div class="aos-item aos-item--7" data-aos-offset="50" data-aos="flip-down"></div>
    <div class="aos-item aos-item--8" data-aos-offset="50" data-aos="flip-left"></div>
    <div class="aos-item aos-item--9" data-aos-offset="50" data-aos="flip-right"></div>
    <div class="aos-item aos-item--10" data-aos-offset="50" data-aos="zoom-in"></div>
    <div class="aos-item aos-item--11" data-aos-offset="50" data-aos="zoom-in-up"></div>
    <div class="aos-item aos-item--12" data-aos-offset="50" data-aos="zoom-in-down"></div>
    <div class="aos-item aos-item--13" data-aos-offset="50" data-aos="zoom-in-left"></div>
    <div class="aos-item aos-item--14" data-aos-offset="50" data-aos="zoom-in-right"></div>
    <div class="aos-item aos-item--15" data-aos-offset="50" data-aos="slide-up"></div>
    <div class="aos-item aos-item--16" data-aos-offset="50" data-aos="slide-down"></div>
    <div class="aos-item aos-item--17" data-aos-offset="50" data-aos="slide-left"></div>
    <div class="aos-item aos-item--18" data-aos-offset="50" data-aos="slide-right"></div>
  </body>
</html>
