<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI响应测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        input[type="text"] {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI响应测试</h1>
        
        <div class="test-section">
            <h3>📡 测试推荐房间</h3>
            <button onclick="testRecommendation()">测试"推荐房间"</button>
            <div id="recommendResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🏠 测试预订201房间</h3>
            <button onclick="testBooking201()">测试"预订201房间"</button>
            <div id="booking201Result" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 测试2楼房间查询</h3>
            <button onclick="testFloor2()">测试"有没有2楼的房间"</button>
            <div id="floor2Result" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>💬 自定义测试</h3>
            <input type="text" id="customInput" placeholder="输入测试消息..." value="">
            <button onclick="testCustom()">发送测试</button>
            <div id="customResult" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response ' + (isError ? 'error' : 'success');
            element.textContent = content;
        }

        async function sendTestMessage(message) {
            try {
                const response = await fetch('http://localhost:8080/ai-chat/send', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        userId: 'test-user'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    return data.data || data.message || '无响应内容';
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                throw new Error(`请求失败: ${error.message}`);
            }
        }

        async function testRecommendation() {
            try {
                showResult('recommendResult', '正在测试推荐房间...', false);
                const result = await sendTestMessage('推荐房间');
                showResult('recommendResult', result, false);
            } catch (error) {
                showResult('recommendResult', error.message, true);
            }
        }

        async function testBooking201() {
            try {
                showResult('booking201Result', '正在测试预订201房间...', false);
                const result = await sendTestMessage('预订201房间');
                showResult('booking201Result', result, false);
            } catch (error) {
                showResult('booking201Result', error.message, true);
            }
        }

        async function testFloor2() {
            try {
                showResult('floor2Result', '正在测试2楼房间查询...', false);
                const result = await sendTestMessage('有没有2楼的房间');
                showResult('floor2Result', result, false);
            } catch (error) {
                showResult('floor2Result', error.message, true);
            }
        }

        async function testCustom() {
            const input = document.getElementById('customInput').value;
            if (!input.trim()) {
                alert('请输入测试消息');
                return;
            }

            try {
                showResult('customResult', `正在测试: ${input}...`, false);
                const result = await sendTestMessage(input);
                showResult('customResult', result, false);
            } catch (error) {
                showResult('customResult', error.message, true);
            }
        }
    </script>
</body>
</html>
