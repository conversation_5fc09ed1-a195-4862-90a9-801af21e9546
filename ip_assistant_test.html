<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP助手功能测试 - 普洱蘑菇庄园民宿</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .result-area {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            min-height: 100px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        .quick-links {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .link-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .link-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            color: white;
        }
        .link-btn.primary {
            background: #007bff;
        }
        .link-btn.success {
            background: #28a745;
        }
        .link-btn.info {
            background: #17a2b8;
        }
        .link-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .demo-section {
            background: #fff3cd;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .demo-video {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #fff;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .demo-video:hover {
            transform: scale(1.1);
        }
        .video-container {
            display: flex;
            gap: 20px;
            align-items: center;
            margin-bottom: 20px;
        }
        .video-info {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 IP助手功能测试中心</h1>
            <p>测试新IP形象、对话功能和周边产品展示</p>
        </div>

        <div class="quick-links">
            <h3>🚀 快速访问</h3>
            <div class="link-buttons">
                <a href="http://localhost:5173/" class="link-btn primary" target="_blank">
                    🏠 主页 (带IP助手)
                </a>
                <a href="http://localhost:5173/products" class="link-btn success" target="_blank">
                    🛍️ 周边产品页面
                </a>
                <a href="http://localhost:5173/ai-room-selection" class="link-btn info" target="_blank">
                    🤖 AI选房页面
                </a>
                <a href="http://localhost:5173/tea-culture" class="link-btn warning" target="_blank">
                    🍵 茶文化页面
                </a>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎬 IP形象视频演示</h3>
            <div class="video-container">
                <video 
                    id="demoVideo1" 
                    class="demo-video" 
                    src="/src/assets/images/IP形象/37b48b40dbc80e2a44dce0f626120357_raw.mp4"
                    autoplay 
                    loop 
                    muted 
                    playsinline
                    onclick="switchVideo(1)"
                ></video>
                <div class="video-info">
                    <h4>默认状态视频</h4>
                    <p>这是IP助手的默认状态，用于吸引用户注意</p>
                    <button onclick="switchVideo(1)" class="btn btn-primary">播放默认视频</button>
                </div>
            </div>
            
            <div class="video-container">
                <video 
                    id="demoVideo2" 
                    class="demo-video" 
                    src="/src/assets/images/IP形象/4e8a0e0ab4bdb3d1738dc2f6bf7fb411_raw.mp4"
                    loop 
                    muted 
                    playsinline
                    onclick="switchVideo(2)"
                ></video>
                <div class="video-info">
                    <h4>激活状态视频</h4>
                    <p>当用户点击IP助手时切换到此视频，表示对话激活</p>
                    <button onclick="switchVideo(2)" class="btn btn-success">播放激活视频</button>
                </div>
            </div>
        </div>

        <div class="feature-grid">
            <!-- IP助手功能测试 -->
            <div class="feature-card">
                <h3>🤖 IP助手功能</h3>
                <div class="test-buttons">
                    <button class="btn btn-primary" onclick="testIPFeature('茶文化')">茶文化介绍</button>
                    <button class="btn btn-primary" onclick="testIPFeature('周边产品')">周边产品推荐</button>
                    <button class="btn btn-primary" onclick="testIPFeature('民宿活动')">民宿活动介绍</button>
                    <button class="btn btn-primary" onclick="testIPFeature('房间预订')">房间预订引导</button>
                </div>
                <div class="result-area" id="ipResult">
                    ✅ IP助手功能说明：
                    • 🍵 茶文化知识介绍
                    • 🛍️ 周边产品推荐
                    • 🎉 民宿活动介绍
                    • 🏠 房间预订引导
                    • 📱 快捷导航功能
                    
                    点击上方按钮测试各项功能！
                </div>
            </div>

            <!-- 周边产品测试 -->
            <div class="feature-card">
                <h3>🛍️ 周边产品展示</h3>
                <div class="test-buttons">
                    <button class="btn btn-success" onclick="testProducts('茶叶')">茶叶产品</button>
                    <button class="btn btn-success" onclick="testProducts('IP衍生品')">IP衍生品</button>
                    <button class="btn btn-success" onclick="testProducts('纪念品')">纪念品</button>
                </div>
                <div class="result-area" id="productResult">
                    🛍️ 周边产品分类：
                    
                    🍵 茶叶产品：
                    • 精品普洱茶饼 (¥168)
                    • 散装普洱茶 (¥88)
                    • 普洱茶礼盒装 (¥298)
                    
                    🌟 IP衍生品：
                    • 茶茶主题帆布包 (¥58)
                    • 茶茶纪念挂坠 (¥28)
                    • 茶茶主题服装 (¥128)
                    
                    🎁 纪念品：
                    • 庄园纪念勋章 (¥38)
                    • 茶茶定制水杯 (¥88)
                    • 平板保护套 (¥68)
                </div>
            </div>

            <!-- 对话功能测试 -->
            <div class="feature-card">
                <h3>💬 对话功能测试</h3>
                <div class="test-buttons">
                    <button class="btn btn-warning" onclick="testChat('你好')">问候测试</button>
                    <button class="btn btn-warning" onclick="testChat('普洱茶文化')">茶文化咨询</button>
                    <button class="btn btn-warning" onclick="testChat('有什么纪念品')">产品咨询</button>
                    <button class="btn btn-warning" onclick="testChat('民宿活动')">活动咨询</button>
                </div>
                <div class="result-area" id="chatResult">等待对话测试...</div>
            </div>

            <!-- 导航功能测试 -->
            <div class="feature-card">
                <h3>🧭 导航功能测试</h3>
                <div class="test-buttons">
                    <button class="btn btn-primary" onclick="testNavigation('房间预订')">房间预订</button>
                    <button class="btn btn-success" onclick="testNavigation('周边产品')">周边产品</button>
                    <button class="btn btn-warning" onclick="testNavigation('茶文化')">茶文化</button>
                    <button class="btn btn-info" onclick="testNavigation('民宿活动')">民宿活动</button>
                </div>
                <div class="result-area" id="navResult">
                    🧭 快捷导航功能：
                    
                    • 🏠 房间预订 → /ai-room-selection
                    • 🛍️ 周边产品 → /products
                    • 🍵 茶文化 → 对话介绍
                    • 🎉 民宿活动 → 对话介绍
                    
                    点击按钮测试导航功能！
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📋 功能清单</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div>
                    <h4>✅ 已实现功能</h4>
                    <ul>
                        <li>🎬 IP形象视频切换</li>
                        <li>💬 智能对话系统</li>
                        <li>🧭 快捷导航功能</li>
                        <li>🛍️ 周边产品展示</li>
                        <li>🍵 茶文化介绍</li>
                        <li>🎉 民宿活动介绍</li>
                        <li>📱 响应式设计</li>
                        <li>🔄 点击开关对话框</li>
                    </ul>
                </div>
                <div>
                    <h4>🎯 核心特性</h4>
                    <ul>
                        <li>🤖 替换除AI选房外的所有页面右下角助手</li>
                        <li>🎬 使用新IP形象MP4视频</li>
                        <li>💬 对话框可点击关闭</li>
                        <li>📏 对话框尺寸优化</li>
                        <li>🛍️ 集成周边产品推荐</li>
                        <li>🍵 茶文化知识介绍</li>
                        <li>🎉 民宿活动介绍</li>
                        <li>🧭 快捷导航到各页面</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchVideo(videoNum) {
            const video1 = document.getElementById('demoVideo1');
            const video2 = document.getElementById('demoVideo2');
            
            if (videoNum === 1) {
                video1.play();
                video2.pause();
                console.log('播放默认状态视频');
            } else {
                video2.play();
                video1.pause();
                console.log('播放激活状态视频');
            }
        }

        function testIPFeature(feature) {
            const responses = {
                '茶文化': `🍃 **普洱茶文化介绍**

🌿 **历史渊源**
普洱茶有着1700多年的历史，是云南特有的地理标志产品。我们庄园位于普洱茶的核心产区，拥有得天独厚的自然条件。

🍵 **制作工艺**
• 采摘：清晨采摘嫩叶
• 杀青：高温破坏酶活性
• 揉捻：形成茶叶条索
• 晒干：自然阳光干燥

🎯 **茶艺体验**
我们提供专业的茶艺表演和体验课程，让您深度了解普洱茶文化。`,

                '周边产品': `🛍️ **精选周边产品**

🍵 **茶叶系列**
• 精品普洱茶饼
• 散装普洱茶
• 特色茶叶礼盒

🎨 **IP衍生产品**
• 茶茶主题帆布包
• 精美茶具套装
• 纪念品挂坠
• 主题服装

🏆 **特色纪念品**
• 庄园专属勋章
• 定制水杯
• 平板保护套

点击"周边产品"查看详细信息和购买！`,

                '民宿活动': `🎉 **民宿特色活动**

🍵 **茶艺体验**
• 专业茶艺师指导
• 学习泡茶技巧
• 品鉴不同茶叶

🍄 **蘑菇采摘**
• 有机蘑菇园参观
• 亲手采摘体验
• 蘑菇料理制作

🌿 **自然探索**
• 茶园漫步
• 生态摄影
• 日出日落观赏

🎨 **文化体验**
• 传统手工艺
• 民族文化表演
• 篝火晚会`,

                '房间预订': `🏠 我可以为您推荐合适的房间！点击上方"房间预订"按钮，我会为您提供专业的选房服务。

🤖 AI选房助手特色：
• 智能房间推荐
• 个性化建议
• 实时价格查询
• 一键预订服务

立即体验AI选房功能！`
            };

            document.getElementById('ipResult').textContent = responses[feature] || '功能测试中...';
        }

        function testProducts(category) {
            const products = {
                '茶叶': `🍵 **茶叶产品详情**

1. 精品普洱茶饼 - ¥168
   选用优质大叶种茶叶，传统工艺制作

2. 散装普洱茶 - ¥88
   新鲜采摘，自然晒干，原汁原味

3. 普洱茶礼盒装 - ¥298
   精美包装，送礼佳品`,

                'IP衍生品': `🌟 **IP衍生产品详情**

1. 茶茶主题帆布包 - ¥58
   可爱IP形象，实用环保

2. 茶茶纪念挂坠 - ¥28
   精美挂坠，随身携带

3. 茶茶主题服装 - ¥128
   舒适面料，时尚设计`,

                '纪念品': `🎁 **纪念品详情**

1. 庄园纪念勋章 - ¥38
   独特设计，收藏价值

2. 茶茶定制水杯 - ¥88
   保温效果好，设计精美

3. 平板保护套 - ¥68
   茶茶图案，保护设备`
            };

            document.getElementById('productResult').textContent = products[category] || '产品加载中...';
        }

        function testChat(message) {
            const responses = {
                '你好': `🍵 您好！欢迎来到普洱蘑菇庄园民宿！我是您的专属AI助手茶茶。

🤖 **我可以为您提供**：
• 🍃 茶文化知识介绍
• 🛍️ 周边产品推荐
• 🎉 民宿活动介绍
• 🏠 房间预订服务

请告诉我您想了解什么！`,

                '普洱茶文化': `🍃 **普洱茶文化深度介绍**

普洱茶是云南特有的地理标志产品，有着悠久的历史和深厚的文化底蕴。

🌿 **文化特色**：
• 历史悠久，传承千年
• 工艺独特，自然发酵
• 越陈越香，收藏价值高
• 养生功效，深受喜爱

我们庄园提供专业的茶艺体验课程！`,

                '有什么纪念品': `🎁 **精选纪念品推荐**

我们有很多精美的纪念品：

🏆 **热门推荐**：
• 庄园专属纪念勋章
• 茶茶主题帆布包
• 定制保温水杯
• 精美茶具套装

🌟 **限量版**：
• 茶茶主题服装
• 平板保护套
• 纪念挂坠

点击"周边产品"查看完整商品目录！`,

                '民宿活动': `🎉 **丰富的民宿活动**

我们为您准备了多种精彩活动：

🍵 **茶文化体验**：
• 茶艺表演和学习
• 茶园参观导览
• 品茶会活动

🍄 **自然体验**：
• 蘑菇采摘活动
• 生态摄影之旅
• 森林徒步探索

🎨 **文化活动**：
• 传统手工艺制作
• 篝火晚会
• 民族文化表演

想参加哪个活动呢？`
            };

            document.getElementById('chatResult').textContent = responses[message] || `您说："${message}"\n\n茶茶正在思考回复...`;
        }

        function testNavigation(nav) {
            const navInfo = {
                '房间预订': `🏠 **房间预订导航**

点击后将跳转到：/ai-room-selection
功能：AI智能选房助手

✨ **特色功能**：
• 智能房间推荐
• 个性化建议
• 实时价格查询
• 一键预订服务`,

                '周边产品': `🛍️ **周边产品导航**

点击后将跳转到：/products
功能：产品展示和购买

🛒 **产品分类**：
• 茶叶产品
• IP衍生品
• 纪念品`,

                '茶文化': `🍵 **茶文化介绍**

通过对话形式介绍茶文化知识
不跳转页面，直接在对话框中展示

📚 **内容包括**：
• 普洱茶历史
• 制作工艺
• 文化传承
• 体验活动`,

                '民宿活动': `🎉 **民宿活动介绍**

通过对话形式介绍活动信息
不跳转页面，直接在对话框中展示

🎯 **活动类型**：
• 茶艺体验
• 蘑菇采摘
• 自然探索
• 文化体验`
            };

            document.getElementById('navResult').textContent = navInfo[nav] || '导航测试中...';
        }

        // 页面加载时自动播放第一个视频
        window.addEventListener('load', () => {
            switchVideo(1);
        });
    </script>
</body>
</html>
