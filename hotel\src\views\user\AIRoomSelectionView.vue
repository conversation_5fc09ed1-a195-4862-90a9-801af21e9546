<template>
  <div class="ai-room-selection">
    <AppNavbar />

    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">
          <i class="fas fa-robot"></i>
          AI智能选房
        </h1>
        <p class="page-subtitle">与茶茶AI助手对话，找到最适合您的房间</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <div class="content-grid">
          <!-- 左侧：AI助手、推荐房间、订单详情 -->
          <div class="left-panel">
            <!-- AI助手 -->
            <div class="ai-assistant">
              <div class="ai-header">
                <div class="ai-avatar" @click="toggleAIVoice">
                  <video
                    ref="aiVideo"
                    :src="currentVideoSrc"
                    alt="茶茶AI助手"
                    :class="['ai-character', aiState]"
                    autoplay
                    loop
                    muted
                    playsinline
                  ></video>
                  <div v-if="isSpeaking" class="speaking-indicator">
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                  </div>
                </div>
                <div class="ai-info">
                  <h3>茶茶 AI助手</h3>
                  <p>专属选房顾问</p>
                  <div class="ai-status">
                    <span class="status-dot" :class="{ online: isDifyConnected }"></span>
                    <span>{{ isDifyConnected ? '在线' : '离线' }}</span>
                  </div>
                </div>
              </div>
              <div class="ai-controls">
                <button @click="toggleAIVoice" :class="['btn', { active: voiceEnabled }]">
                  <i :class="voiceEnabled ? 'fas fa-volume-up' : 'fas fa-volume-mute'"></i>
                  {{ voiceEnabled ? '关闭语音' : '开启语音' }}
                </button>
                <button @click="testDifyConnection" class="btn">
                  <i class="fas fa-plug"></i>
                  测试连接
                </button>
              </div>
            </div>

            <!-- 推荐房间 -->
            <div class="recommended-rooms">
              <h3>
                <i class="fas fa-star"></i>
                AI推荐房间
                <span v-if="aiRecommendations.length > 0" class="count">({{ aiRecommendations.length }})</span>
              </h3>
              <div class="rooms-list">
                <div v-if="aiRecommendations.length === 0" class="empty-state">
                  <i class="fas fa-robot"></i>
                  <p>请与AI助手对话，我会为您推荐最适合的房间</p>
                </div>
                <div v-else class="rooms">
                  <div
                    v-for="room in aiRecommendations"
                    :key="room.id"
                    class="room-card"
                    @click="selectRoom(room)"
                  >
                    <img :src="room.image" :alt="room.name" class="room-image">
                    <div class="room-info">
                      <h4>{{ room.code }}号房</h4>
                      <p>{{ room.name }}</p>
                      <div class="room-meta">
                        <span class="price">¥{{ room.price }}/晚</span>
                        <span class="rating">⭐{{ room.rating }}</span>
                        <span class="status" :class="room.status">
                          {{ room.status === 'available' ? '可预订' : '已预订' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 订单详情 -->
            <div class="order-details">
              <h3>
                <i class="fas fa-receipt"></i>
                订单详情
              </h3>
              <div class="order-content">
                <div v-if="currentOrder" class="order-info">
                  <div class="order-item">
                    <span class="label">订单号：</span>
                    <span>{{ currentOrder.orderNumber }}</span>
                  </div>
                  <div class="order-item">
                    <span class="label">房间：</span>
                    <span>{{ currentOrder.roomCode }}号房</span>
                  </div>
                  <div class="order-item">
                    <span class="label">入住：</span>
                    <span>{{ currentOrder.checkInDate }}</span>
                  </div>
                  <div class="order-item">
                    <span class="label">退房：</span>
                    <span>{{ currentOrder.checkOutDate }}</span>
                  </div>
                  <div class="order-item total">
                    <span class="label">总计：</span>
                    <span class="price">¥{{ currentOrder.totalAmount }}</span>
                  </div>
                  <div class="order-actions">
                    <button class="btn btn-outline" @click="cancelOrder">取消订单</button>
                    <button class="btn btn-primary" @click="modifyOrder">修改订单</button>
                  </div>
                </div>
                <div v-else class="empty-state">
                  <i class="fas fa-clipboard-list"></i>
                  <p>暂无订单信息</p>
                  <p class="empty-hint">完成房间选择后，订单详情将显示在这里</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：AI聊天 -->
          <div class="right-panel">
            <div class="chat-container">
              <div class="chat-header">
                <h3>
                  <i class="fas fa-comments"></i>
                  与AI助手对话
                </h3>
                <div class="chat-status">
                  <span class="status-dot" :class="{ online: isDifyConnected }"></span>
                  <span>{{ isDifyConnected ? '在线' : '离线' }}</span>
                </div>
              </div>

              <!-- 聊天消息 -->
              <div class="chat-messages" ref="fallbackChatContainer">
                <div v-for="(message, index) in fallbackMessages" :key="index" :class="['message', message.type]">
                  <div class="message-avatar" v-if="message.type === 'ai'">
                    <img src="/src/assets/images/IP形象/茶茶正面.png" alt="AI助手">
                  </div>
                  <div class="message-content">
                    <div class="message-text" v-html="convertMarkdownLinks(message.content)"></div>
                    <div class="message-time">{{ message.timestamp }}</div>
                  </div>
                </div>

                <!-- 输入提示 -->
                <div v-if="isFallbackLoading" class="typing-indicator">
                  <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                  <span class="typing-text">AI助手正在思考...</span>
                </div>
              </div>
            </div>

            <!-- 聊天输入区域 - 移到容器底部 -->
            <div class="chat-input-section">
              <!-- 快捷问题 -->
              <div class="quick-questions" v-if="fallbackMessages.length <= 1">
                <button class="quick-btn" @click="sendQuickQuestion('我想预订一间房')">
                  <i class="fas fa-bed"></i>
                  我想预订房间
                </button>
                <button class="quick-btn" @click="sendQuickQuestion('有什么房型推荐？')">
                  <i class="fas fa-star"></i>
                  房型推荐
                </button>
                <button class="quick-btn" @click="sendQuickQuestion('查看房间价格')">
                  <i class="fas fa-tag"></i>
                  价格查询
                </button>
              </div>

              <!-- 输入框 -->
              <div class="input-container">
                <input
                  v-model="fallbackInput"
                  @keyup.enter="sendFallbackMessage"
                  placeholder="请告诉我您的需求，比如：我想要一间双人房..."
                  class="chat-input"
                  :disabled="isFallbackLoading"
                />
                <button
                  @click="sendFallbackMessage"
                  class="send-btn"
                  :disabled="isFallbackLoading || !fallbackInput.trim()"
                >
                  <i class="fas fa-paper-plane"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 全部房间展示 -->
        <div class="all-rooms-section">
          <div class="section-header">
            <h2>
              <i class="fas fa-home"></i>
              全部房间
            </h2>
            <div class="room-filters">
              <select v-model="roomFilter" @change="filterRooms" class="filter-select">
                <option value="all">全部房型</option>
                <option value="single">单人房</option>
                <option value="double">双人房</option>
                <option value="family">家庭房</option>
                <option value="suite">套房</option>
              </select>
              <div class="view-toggle">
                <button
                  :class="['toggle-btn', { active: viewMode === 'grid' }]"
                  @click="viewMode = 'grid'"
                >
                  <i class="fas fa-th"></i>
                </button>
                <button
                  :class="['toggle-btn', { active: viewMode === 'list' }]"
                  @click="viewMode = 'list'"
                >
                  <i class="fas fa-list"></i>
                </button>
              </div>
            </div>
          </div>

          <div class="rooms-content">
            <div v-if="roomsLoading" class="loading-state">
              <div class="loading-spinner"></div>
              <p>正在加载房间信息...</p>
            </div>

            <div v-else-if="filteredRooms.length === 0" class="empty-state">
              <i class="fas fa-home"></i>
              <p>暂无符合条件的房间</p>
            </div>

            <div v-else :class="['rooms-grid', viewMode]">
              <div
                v-for="room in filteredRooms"
                :key="room.id"
                class="room-item"
                @click="selectRoom(room)"
              >
                <div class="room-image-container">
                  <img :src="room.image" :alt="room.name" class="room-image">
                  <div class="room-status-badge" :class="room.status">
                    {{ room.status === 'available' ? '可预订' : '已预订' }}
                  </div>
                  <div class="room-price-badge">
                    {{ room.price > 0 ? `¥${room.price}/晚` : '价格待定' }}
                  </div>
                </div>
                <div class="room-details">
                  <div class="room-header">
                    <h4 class="room-name">{{ room.code }}号房 - {{ room.roomType }}</h4>
                    <div class="room-rating" v-if="room.rating > 0">
                      <i class="fas fa-star"></i>
                      <span>{{ room.rating }}</span>
                    </div>
                  </div>
                  <p class="room-type">{{ room.name }} ({{ room.floor }})</p>
                  <div class="room-features">
                    <span class="feature" v-if="room.seat > 0">
                      <i class="fas fa-users"></i>
                      {{ room.seat }}人
                    </span>
                    <span class="feature">
                      <i class="fas fa-window-maximize"></i>
                      {{ room.hasWindow ? `${room.windowDirection}向` : '无窗' }}
                    </span>
                    <span class="feature">
                      <i class="fas fa-wifi"></i>
                      免费WiFi
                    </span>
                    <span class="feature">
                      <i class="fas fa-snowflake"></i>
                      空调
                    </span>
                    <span class="feature" v-if="room.vrUrl">
                      <i class="fas fa-vr-cardboard"></i>
                      <a :href="room.vrUrl" target="_blank" class="vr-link">VR看房</a>
                    </span>
                  </div>
                  <div class="room-footer">
                    <div class="room-category">{{ room.category }}</div>
                    <button
                      class="book-btn"
                      :class="{ disabled: room.status !== 'available' || room.price === 0 }"
                      :disabled="room.status !== 'available' || room.price === 0"
                      @click.stop="selectRoom(room)"
                    >
                      {{ room.price === 0 ? '价格待定' : (room.status === 'available' ? '选择房间' : '已预订') }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import request from '../../utils/request'
import AppNavbar from '../../components/AppNavbar.vue'
import { IntelligentAI, type Room } from '../../utils/intelligentAI'
import { useChatHistoryStore } from '../../stores/chatHistory'
import { OrderService, type OrderCreateData } from '../../services/orderService'

const router = useRouter()
const auth = useAuthStore()
const chatHistory = useChatHistoryStore()

// 基础状态
const isDifyConnected = ref(false)
const voiceEnabled = ref(false)
const isSpeaking = ref(false)
const aiState = ref('idle')
const currentVideoSrc = ref('/src/assets/images/IP形象/37b48b40dbc80e2a44dce0f626120357_raw.mp4')

// AI推荐房间
const aiRecommendations = ref<Room[]>([])

// 当前订单
const currentOrder = ref<any>(null)

// 聊天相关
const fallbackMessages = ref<any[]>([])
const fallbackInput = ref('')
const isFallbackLoading = ref(false)
const fallbackChatContainer = ref<HTMLElement>()

// AI视频引用
const aiVideo = ref<HTMLVideoElement>()

// 全部房间相关
const allRooms = ref<any[]>([])
const filteredRooms = ref<any[]>([])
const roomsLoading = ref(false)
const roomFilter = ref('all')
const viewMode = ref('grid')

// 初始化
onMounted(() => {
  initializeAI()
  loadInitialData()
})

// 初始化AI
const initializeAI = () => {
  // 添加欢迎消息
  fallbackMessages.value.push({
    type: 'ai',
    content: '您好！我是茶茶AI助手，很高兴为您服务！请告诉我您的需求，我会为您推荐最适合的房间。',
    timestamp: new Date().toLocaleTimeString()
  })
}

// 加载初始数据
const loadInitialData = async () => {
  await loadAllRooms()
}

// 加载全部房间
const loadAllRooms = async () => {
  roomsLoading.value = true
  try {
    // 真实房间数据
    const realRooms: any[] = [
      {
        id: 1,
        code: '展览厅',
        name: '展览厅',
        category: '展览厅',
        floor: '1楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '展览厅',
        price: 0,
        seat: 0,
        rating: 0,
        status: 'unavailable',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=300&fit=crop',
        vrUrl: ''
      },
      {
        id: 2,
        code: '105',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '单人间',
        price: 256,
        seat: 1,
        rating: 4.5,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 3,
        code: '104',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '单人间',
        price: 255,
        seat: 1,
        rating: 4.5,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 4,
        code: '103',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '单人间',
        price: 333,
        seat: 1,
        rating: 4.6,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 5,
        code: '102',
        name: '雨林景观豪华蘑菇屋',
        category: '大床房',
        floor: '1楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '东',
        roomType: '大床房',
        price: 888,
        seat: 2,
        rating: 4.8,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 6,
        code: '106',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 666,
        seat: 1,
        rating: 4.7,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 7,
        code: '107',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 456,
        seat: 1,
        rating: 4.6,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 8,
        code: '108',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 156,
        seat: 1,
        rating: 4.4,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 9,
        code: '101',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '1楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '东',
        roomType: '单人间',
        price: 18,
        seat: 1,
        rating: 4.2,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 10,
        code: '211',
        name: '雨林景观豪华蘑菇屋',
        category: '亲子房',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '西',
        roomType: '亲子房',
        price: 419,
        seat: 3,
        rating: 4.7,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 11,
        code: '212',
        name: '雨林景观豪华蘑菇屋',
        category: '亲子房',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '西',
        roomType: '亲子房',
        price: 418,
        seat: 3,
        rating: 4.7,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 12,
        code: '210',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '北',
        roomType: '单人间',
        price: 189,
        seat: 1,
        rating: 4.4,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 13,
        code: '209',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '北',
        roomType: '单人间',
        price: 418,
        seat: 1,
        rating: 4.6,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 14,
        code: '208',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '北',
        roomType: '单人间',
        price: 18,
        seat: 1,
        rating: 4.2,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 15,
        code: '207',
        name: '雨林景观豪华蘑菇屋',
        category: '大床房',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '东',
        roomType: '大床房',
        price: 186,
        seat: 2,
        rating: 4.5,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 16,
        code: '206',
        name: '雨林景观豪华蘑菇屋',
        category: '双人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '西',
        roomType: '双人间',
        price: 654,
        seat: 2,
        rating: 4.7,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 17,
        code: '205',
        name: '雨林景观豪华蘑菇屋',
        category: '双人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '东',
        roomType: '双人间',
        price: 789,
        seat: 2,
        rating: 4.8,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 18,
        code: '204',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 999,
        seat: 1,
        rating: 4.9,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 19,
        code: '203',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 889,
        seat: 1,
        rating: 4.8,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 20,
        code: '202',
        name: '雨林景观豪华蘑菇屋',
        category: '单人间',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '单人间',
        price: 48,
        seat: 1,
        rating: 4.3,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 21,
        code: '201',
        name: '雨林景观豪华蘑菇屋',
        category: '大床房',
        floor: '2楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '大床房',
        price: 19,
        seat: 2,
        rating: 4.1,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 22,
        code: '301',
        name: '雨林景观豪华蘑菇屋',
        category: '亲子房',
        floor: '3楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '亲子房',
        price: 486,
        seat: 4,
        rating: 4.8,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 23,
        code: '302',
        name: '雨林景观豪华蘑菇屋',
        category: '亲子房',
        floor: '3楼',
        height: '2438',
        hasWindow: false,
        windowDirection: '无',
        roomType: '亲子房',
        price: 1088,
        seat: 4,
        rating: 4.9,
        status: 'available',
        image: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      },
      {
        id: 24,
        code: '303',
        name: '雨林景观豪华蘑菇屋',
        category: '双人间',
        floor: '3楼',
        height: '2438',
        hasWindow: true,
        windowDirection: '南',
        roomType: '双人间',
        price: 0, // 价格未设定
        seat: 2,
        rating: 4.6,
        status: 'unavailable', // 价格未设定，暂不可预订
        image: 'https://images.unsplash.com/photo-1618773928121-c32242e63f39?w=400&h=300&fit=crop',
        vrUrl: 'https://share-xt.hwbim.com/qlh/ffd333620ebb4e83b50899653b516f00'
      }
    ]

    allRooms.value = realRooms
    filteredRooms.value = realRooms
  } catch (error) {
    console.error('加载房间数据失败:', error)
  } finally {
    roomsLoading.value = false
  }
}

// 过滤房间
const filterRooms = () => {
  if (roomFilter.value === 'all') {
    filteredRooms.value = allRooms.value.filter(room => room.code !== '展览厅') // 排除展览厅
  } else {
    filteredRooms.value = allRooms.value.filter(room => {
      if (room.code === '展览厅') return false // 排除展览厅

      switch (roomFilter.value) {
        case 'single':
          return room.roomType === '单人间'
        case 'double':
          return room.roomType === '双人间' || room.roomType === '大床房'
        case 'family':
          return room.roomType === '亲子房'
        case 'suite':
          return room.category.includes('套房') || room.category.includes('豪华')
        default:
          return true
      }
    })
  }
}

// 切换AI语音
const toggleAIVoice = () => {
  voiceEnabled.value = !voiceEnabled.value
}

// 测试Dify连接
const testDifyConnection = () => {
  isDifyConnected.value = !isDifyConnected.value
}

// 选择房间
const selectRoom = (room: Room) => {
  console.log('选择房间:', room)
}

// 发送消息
const sendFallbackMessage = () => {
  if (!fallbackInput.value.trim()) return
  
  // 添加用户消息
  fallbackMessages.value.push({
    type: 'user',
    content: fallbackInput.value,
    timestamp: new Date().toLocaleTimeString()
  })
  
  const userMessage = fallbackInput.value
  fallbackInput.value = ''
  
  // 模拟AI回复
  isFallbackLoading.value = true
  setTimeout(() => {
    fallbackMessages.value.push({
      type: 'ai',
      content: `我收到了您的需求："${userMessage}"。让我为您推荐一些合适的房间...`,
      timestamp: new Date().toLocaleTimeString()
    })
    isFallbackLoading.value = false
    scrollToBottom()
  }, 1000)
}

// 发送快捷问题
const sendQuickQuestion = (question: string) => {
  fallbackInput.value = question
  sendFallbackMessage()
}

// 转换Markdown链接
const convertMarkdownLinks = (content: string) => {
  return content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (fallbackChatContainer.value) {
      fallbackChatContainer.value.scrollTop = fallbackChatContainer.value.scrollHeight
    }
  })
}

// 获取表情符号
const getExpressionEmoji = () => {
  return '😊'
}

// 获取AI状态文本
const aiStatusText = ref('我会根据您的需求为您推荐最适合的房间')

// 取消订单
const cancelOrder = () => {
  if (currentOrder.value) {
    currentOrder.value = null
    fallbackMessages.value.push({
      type: 'ai',
      content: '您的订单已取消。如需重新预订，请告诉我您的需求。',
      timestamp: new Date().toLocaleTimeString()
    })
  }
}

// 修改订单
const modifyOrder = () => {
  if (currentOrder.value) {
    fallbackMessages.value.push({
      type: 'ai',
      content: '请告诉我您想要修改的内容，比如入住日期、退房日期等。',
      timestamp: new Date().toLocaleTimeString()
    })
  }
}
</script>

<style scoped>
/* 基础样式 */
.ai-room-selection {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem 0;
  margin-top: 70px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

/* 主要内容 */
.main-content {
  padding: 2rem 0;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  min-height: 800px;
  margin-bottom: 3rem;
}

/* 左侧面板 */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  height: fit-content;
}

/* AI助手 */
.ai-assistant {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  min-height: 200px;
}

.ai-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.ai-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  transition: transform 0.3s ease;
}

.ai-avatar:hover {
  transform: scale(1.05);
}

.ai-character {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.speaking-indicator {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 2px;
}

.wave {
  width: 3px;
  height: 15px;
  background: #d4af37;
  border-radius: 2px;
  animation: wave 1s ease-in-out infinite;
}

.wave:nth-child(2) {
  animation-delay: 0.2s;
}

.wave:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes wave {
  0%, 100% { height: 15px; }
  50% { height: 25px; }
}

.ai-info h3 {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.3rem;
}

.ai-info p {
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

.status-dot.online {
  background: #10b981;
}

.ai-controls {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.btn.active {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

/* 推荐房间 */
.recommended-rooms {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  min-height: 300px;
  flex: 1;
}

.recommended-rooms h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.count {
  font-size: 0.9rem;
  color: #d4af37;
}

.rooms {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.room-card {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.room-card:hover {
  border-color: #d4af37;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.room-image {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.room-info h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.3rem;
}

.room-info p {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.room-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
}

.price {
  color: #d4af37;
  font-weight: 600;
}

.rating {
  color: #f59e0b;
}

.status.available {
  color: #10b981;
}

.status.unavailable {
  color: #ef4444;
}

/* 订单详情 */
.order-details {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  min-height: 250px;
}

.order-details h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.order-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.order-item.total {
  border-bottom: none;
  font-weight: 600;
  color: #d4af37;
}

.label {
  color: #6b7280;
}

.order-actions {
  margin-top: 1rem;
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.btn-outline {
  background: white;
  color: #ef4444;
  border: 1px solid #ef4444;
}

.btn-outline:hover {
  background: #ef4444;
  color: white;
}

.btn-primary {
  background: #d4af37;
  color: white;
  border: 1px solid #d4af37;
}

.btn-primary:hover {
  background: #b8941f;
  border-color: #b8941f;
}

.empty-hint {
  font-size: 0.8rem;
  color: #9ca3af;
  margin-top: 0.5rem;
}

/* 右侧面板 */
.right-panel {
  display: flex;
  flex-direction: column;
}

/* 聊天容器 */
.chat-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 600px;
}

.chat-header {
  padding: 2rem 2rem 1rem;
  border-bottom: 1px solid #f3f4f6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  font-size: 1.2rem;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chat-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #6b7280;
}

/* 聊天消息 */
.chat-messages {
  flex: 1;
  padding: 1rem 2rem;
  overflow-y: auto;
  max-height: 400px;
  min-height: 300px;
}

.message {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-content {
  flex: 1;
  max-width: 80%;
}

.message-text {
  background: #f3f4f6;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.3rem;
}

.message.user .message-text {
  background: #d4af37;
  color: white;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* 输入提示 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #f3f4f6;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.typing-dots {
  display: flex;
  gap: 0.2rem;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #9ca3af;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-8px); }
}

.typing-text {
  font-size: 0.8rem;
  color: #6b7280;
}

/* 聊天输入 */
.chat-input-section {
  padding: 1rem 2rem 2rem;
  border-top: 1px solid #f3f4f6;
  margin-top: auto; /* 推到底部 */
  background: rgba(255, 255, 255, 0.98);
  border-radius: 0 0 20px 20px;
}

.quick-questions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.quick-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.quick-btn:hover {
  background: #d4af37;
  color: white;
}

.input-container {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 25px;
  outline: none;
  font-size: 0.9rem;
}

.chat-input:focus {
  border-color: #d4af37;
  box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.send-btn {
  width: 40px;
  height: 40px;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  background: #b8941f;
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

.empty-state i {
  font-size: 3rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

/* 全部房间展示 */
.all-rooms-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f3f4f6;
}

.section-header h2 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.room-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-select {
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #6b7280;
  cursor: pointer;
}

.view-toggle {
  display: flex;
  gap: 0.5rem;
}

.toggle-btn {
  width: 36px;
  height: 36px;
  border: 1px solid #d1d5db;
  background: white;
  color: #6b7280;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.toggle-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.toggle-btn.active {
  background: #d4af37;
  color: white;
  border-color: #d4af37;
}

.loading-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #d4af37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.rooms-grid {
  display: grid;
  gap: 1.5rem;
}

.rooms-grid.grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.rooms-grid.list {
  grid-template-columns: 1fr;
}

.room-item {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.room-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: #d4af37;
}

.room-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.room-image-container .room-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.room-item:hover .room-image {
  transform: scale(1.05);
}

.room-status-badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.room-status-badge.available {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.room-status-badge.unavailable {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.room-price-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.room-details {
  padding: 1.5rem;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.room-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #1f2937;
}

.room-rating {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #f59e0b;
  font-size: 0.9rem;
  font-weight: 600;
}

.room-type {
  color: #6b7280;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.room-features {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #6b7280;
  font-size: 0.8rem;
}

.feature i {
  color: #d4af37;
}

.vr-link {
  color: #d4af37;
  text-decoration: none;
  font-weight: 500;
}

.vr-link:hover {
  text-decoration: underline;
}

.room-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.room-category {
  background: rgba(212, 175, 55, 0.1);
  color: #d4af37;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.book-btn {
  padding: 0.75rem 1.5rem;
  background: #d4af37;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.book-btn:hover:not(.disabled) {
  background: #b8941f;
  transform: translateY(-2px);
}

.book-btn.disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .rooms-grid.grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .ai-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .ai-controls {
    justify-content: center;
  }

  .quick-questions {
    flex-direction: column;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .room-filters {
    width: 100%;
    justify-content: space-between;
  }

  .rooms-grid.grid {
    grid-template-columns: 1fr;
  }
}
</style>
