<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dify URL 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .response {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Dify URL 连接测试</h1>
        
        <div class="test-section">
            <h3>📡 测试不同的Dify URL</h3>
            <button onclick="testUrl('http://4295a4ce.r28.cpolar.top')">测试基础URL</button>
            <button onclick="testUrl('http://4295a4ce.r28.cpolar.top/v1')">测试 /v1</button>
            <button onclick="testUrl('http://4295a4ce.r28.cpolar.top/v1/chat-messages')">测试 /v1/chat-messages</button>
            <button onclick="testUrl('http://47be5268.r28.cpolar.top')">测试备用URL</button>
            <div id="urlResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🤖 测试Dify API调用</h3>
            <button onclick="testDifyAPI('http://4295a4ce.r28.cpolar.top/v1')">测试API调用</button>
            <button onclick="testDifyAPI('http://47be5268.r28.cpolar.top/v1')">测试备用API</button>
            <div id="apiResult" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 网络诊断</h3>
            <button onclick="diagnoseNetwork()">网络诊断</button>
            <div id="diagResult" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showResult(elementId, content, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${type}`;
            element.textContent = content;
        }

        async function testUrl(url) {
            try {
                showResult('urlResult', `正在测试: ${url}...`, 'warning');
                
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors'
                });

                let result = `URL: ${url}\n`;
                result += `状态: ${response.status} ${response.statusText}\n`;
                result += `可访问: ${response.ok ? '是' : '否'}\n`;
                
                if (response.ok) {
                    const text = await response.text();
                    result += `响应长度: ${text.length} 字符\n`;
                    result += `响应预览: ${text.substring(0, 200)}...`;
                } else {
                    result += `错误信息: ${response.statusText}`;
                }

                showResult('urlResult', result, response.ok ? 'success' : 'error');
            } catch (error) {
                showResult('urlResult', `测试失败: ${error.message}`, 'error');
            }
        }

        async function testDifyAPI(baseUrl) {
            try {
                showResult('apiResult', `正在测试Dify API: ${baseUrl}...`, 'warning');
                
                const response = await fetch(`${baseUrl}/chat-messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer app-oaUwvb7k2zbC8Bi03EO977nN',
                    },
                    body: JSON.stringify({
                        inputs: {},
                        query: '测试消息',
                        response_mode: 'blocking',
                        conversation_id: '',
                        user: 'test-user'
                    })
                });

                let result = `API URL: ${baseUrl}/chat-messages\n`;
                result += `状态: ${response.status} ${response.statusText}\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    result += `成功: API响应正常\n`;
                    result += `响应: ${JSON.stringify(data, null, 2)}`;
                    showResult('apiResult', result, 'success');
                } else {
                    const errorText = await response.text();
                    result += `失败: ${errorText}`;
                    showResult('apiResult', result, 'error');
                }
            } catch (error) {
                showResult('apiResult', `API测试失败: ${error.message}`, 'error');
            }
        }

        async function diagnoseNetwork() {
            let result = '🔍 网络诊断结果:\n\n';
            
            // 测试基本连接
            const urls = [
                'http://4295a4ce.r28.cpolar.top',
                'http://47be5268.r28.cpolar.top',
                'https://www.baidu.com'
            ];

            for (const url of urls) {
                try {
                    const start = Date.now();
                    const response = await fetch(url, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    const time = Date.now() - start;
                    result += `✅ ${url}: 可访问 (${time}ms)\n`;
                } catch (error) {
                    result += `❌ ${url}: 不可访问 (${error.message})\n`;
                }
            }

            result += '\n📋 建议:\n';
            result += '1. 检查Dify服务是否正在运行\n';
            result += '2. 确认cpolar隧道是否有效\n';
            result += '3. 验证API Key是否正确\n';
            result += '4. 检查防火墙设置\n';

            showResult('diagResult', result, 'warning');
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            testUrl('http://4295a4ce.r28.cpolar.top');
        });
    </script>
</body>
</html>
