// 模拟Dify服务器
// 运行命令: node mock_dify_server.js

const express = require('express');
const cors = require('cors');
const app = express();
const port = 3001;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟智能回复数据
const intelligentResponses = {
  '推荐房间': [
    '🍄 根据您的需求，我为您精心推荐以下房间：\n\n🏠 **201号房** - 雨林景观豪华蘑菇屋\n💰 价格：19元/晚\n🛏️ 类型：大床房\n📐 面积：25㎡\n🌿 特色：2楼南向，采光极佳，可观赏茶园美景\n\n🏠 **103号房** - 雨林景观豪华蘑菇屋\n💰 价格：333元/晚\n🛏️ 类型：单人间\n📐 面积：22㎡\n🌿 特色：1楼，独立阳台，适合独自旅行\n\n🏠 **102号房** - 雨林景观豪华蘑菇屋\n💰 价格：888元/晚\n🛏️ 类型：大床房\n📐 面积：40㎡\n🌿 特色：豪华套房，配备茶艺台\n\n✨ 所有房间都包含免费早餐和茶文化体验。您比较倾向于哪种类型呢？',
    
    '🌿 让我为您推荐几间特色房间：\n\n🍄 **经济实惠型**\n• 201号房：19元/晚，性价比超高\n• 101号房：18元/晚，简约舒适\n\n🍄 **舒适体验型**\n• 103号房：333元/晚，独立空间\n• 104号房：255元/晚，温馨雅致\n\n🍄 **豪华享受型**\n• 102号房：888元/晚，顶级配置\n\n每间房都有独特的蘑菇主题装饰和茶文化元素。您的预算范围是多少呢？我可以为您做更精准的推荐。'
  ],
  
  '你好': [
    '🍄 您好！欢迎来到普洱蘑菇庄园民宿！我是您的专属AI助手普普1.0。\n\n我可以为您提供：\n• 🏠 智能房间推荐\n• 📅 预订服务\n• 🍵 茶文化体验介绍\n• 🗺️ 周边景点推荐\n• 🍽️ 美食指南\n\n请告诉我您需要什么帮助，我会根据您的具体需求提供个性化建议！',
    
    '🌿 您好！很高兴为您服务！我是普洱蘑菇庄园的AI助手，基于先进的人工智能技术。\n\n🤔 我正在思考如何为您提供最好的服务...\n\n✨ 我了解到您刚刚到访我们的网站，让我为您介绍一下：\n• 我们是一家以茶文化和蘑菇主题为特色的民宿\n• 拥有24间风格各异的主题房间\n• 提供正宗的普洱茶体验和蘑菇美食\n\n您是想了解房间信息，还是对我们的特色体验感兴趣呢？'
  ],
  
  '有没有2楼的房间': [
    '🏠 当然有！我们2楼有很多优质房间：\n\n🌟 **2楼房间推荐**\n\n🍄 **201号房** - 明星房型\n💰 19元/晚 (超值特价！)\n🛏️ 大床房，25㎡\n🌅 南向采光，茶园景观\n\n🍄 **202号房** - 云雾山景房\n💰 280元/晚\n🛏️ 标准间，30㎡\n🏔️ 山景视野，云雾缭绕\n\n🍄 **203-212号房** - 系列主题房\n💰 价格区间：250-450元/晚\n🎨 每间都有独特的蘑菇主题装饰\n\n🤔 我在思考您的需求... 2楼的优势是：\n• 视野更开阔\n• 相对安静\n• 采光更好\n• 可以俯瞰茶园\n\n您比较倾向于哪个价位区间呢？我可以为您详细介绍具体房间。',
    
    '🏢 2楼确实是我们的热门楼层！让我为您分析一下：\n\n💭 **思考中...** 根据您的询问，我理解您可能希望：\n• 更好的视野和采光\n• 相对安静的环境\n• 避开1楼的人流\n\n🍄 **2楼房间特色**\n• 201-212号房，共12间\n• 价格范围：19-450元/晚\n• 房型：单人间、大床房、标准间\n• 特色：茶园景观、山景视野\n\n🌟 **特别推荐201号房**\n这是我们的明星房型，虽然只要19元/晚，但品质绝不打折！很多客人都说性价比超高。\n\n您希望我为您预订哪一间呢？或者您还想了解其他楼层的房间？'
  ],
  
  '预订201': [
    '🎉 太好了！201号房是我们的明星房型，您的选择很棒！\n\n🏠 **您选择的房间信息**\n• 房间号：201\n• 房型：雨林景观豪华蘑菇屋\n• 价格：19元/晚\n• 面积：25㎡\n• 床型：大床房\n• 位置：2楼南向\n• 特色：茶园景观，采光极佳\n\n💭 **我正在为您处理预订...** \n\n📅 **请告诉我您的入住信息**\n• 入住日期：？\n• 退房日期：？\n• 入住人数：？\n\n您可以这样告诉我：\n"入住2025-07-15，退房2025-07-17，2人入住"\n或者\n"明天入住，后天退房，1人"\n\n🎁 **预订福利**\n• 免费早餐\n• 免费茶艺体验\n• 免费WiFi\n• 24小时热水\n\n我已经为您预留了这个房间，请尽快确认入住日期哦！'
  ]
};

// 获取智能回复
function getIntelligentResponse(query) {
  const lowerQuery = query.toLowerCase();
  
  // 匹配关键词
  for (const [key, responses] of Object.entries(intelligentResponses)) {
    if (lowerQuery.includes(key.toLowerCase()) || 
        (key === '推荐房间' && (lowerQuery.includes('推荐') || lowerQuery.includes('房间'))) ||
        (key === '你好' && (lowerQuery.includes('你好') || lowerQuery.includes('您好') || lowerQuery.includes('hello'))) ||
        (key === '有没有2楼的房间' && (lowerQuery.includes('2楼') || lowerQuery.includes('二楼'))) ||
        (key === '预订201' && (lowerQuery.includes('预订') && lowerQuery.includes('201')))) {
      
      // 随机选择一个回复
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      return randomResponse;
    }
  }
  
  // 默认智能回复
  return `🤔 让我思考一下您的问题："${query}"\n\n💭 **分析中...** 我理解您可能想了解：\n• 房间信息和预订\n• 民宿特色和服务\n• 周边景点和活动\n• 茶文化体验\n\n🍄 我是普洱蘑菇庄园的AI助手，虽然我可能没有完全理解您的具体需求，但我很乐意为您提供帮助！\n\n您可以尝试问我：\n• "推荐房间"\n• "预订201房间"\n• "有什么特色体验"\n• "周边有什么好玩的"\n\n或者直接告诉我您想了解什么，我会尽力为您解答！`;
}

// Dify API 端点
app.post('/v1/chat-messages', (req, res) => {
  const { query, response_mode, user } = req.body;
  
  console.log(`收到消息: ${query} (用户: ${user})`);
  
  // 模拟思考时间
  setTimeout(() => {
    const response = getIntelligentResponse(query);
    
    if (response_mode === 'streaming') {
      // 流式响应
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
      });
      
      // 发送流式数据
      const chunks = response.split('');
      let index = 0;
      
      const sendChunk = () => {
        if (index < chunks.length) {
          const chunk = chunks.slice(index, index + 5).join('');
          res.write(`data: ${JSON.stringify({
            event: 'message',
            answer: chunk,
            conversation_id: 'mock-conversation-' + Date.now()
          })}\n\n`);
          index += 5;
          setTimeout(sendChunk, 50); // 模拟打字效果
        } else {
          res.write(`data: ${JSON.stringify({
            event: 'message_end',
            conversation_id: 'mock-conversation-' + Date.now()
          })}\n\n`);
          res.write('data: [DONE]\n\n');
          res.end();
        }
      };
      
      sendChunk();
    } else {
      // 阻塞式响应
      res.json({
        answer: response,
        conversation_id: 'mock-conversation-' + Date.now(),
        message_id: 'mock-message-' + Date.now()
      });
    }
  }, 500); // 模拟500ms思考时间
});

// 健康检查
app.get('/', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: '模拟Dify服务运行中',
    endpoints: ['/v1/chat-messages']
  });
});

app.get('/v1', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Dify API v1 模拟服务',
    version: '1.0.0'
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`🤖 模拟Dify服务启动成功！`);
  console.log(`📡 服务地址: http://localhost:${port}`);
  console.log(`🔗 API端点: http://localhost:${port}/v1/chat-messages`);
  console.log(`\n📋 使用方法:`);
  console.log(`1. 更新配置文件中的Dify URL为: http://localhost:${port}`);
  console.log(`2. 重启前端和后端服务`);
  console.log(`3. 测试AI对话功能`);
});
